// OpenRouter API configuration
const OPENROUTER_API_KEY = "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";
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Serper API configuration
const SERPER_API_KEY = "5e5eba5e509e26a2ca11b831913e3bd5aeabb459";

// Function to perform web search using Serper API
async function performSerperSearch({ query, num = 10, gl = 'us', hl = 'en' }) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API
async function performSerperScrape({ url }) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}

// Define function declarations for OpenRouter (OpenAI format)
const tools = [
  {
    type: "function",
    function: {
      name: "search_web",
      description: "Search the web using Serper API to get current information",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query"
          },
          num: {
            type: "number",
            description: "Number of results to return (default: 10)"
          },
          gl: {
            type: "string",
            description: "Region code (default: 'us')"
          },
          hl: {
            type: "string",
            description: "Language code (default: 'en')"
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "scrape_webpage",
      description: "Scrape content from a specific webpage URL",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "The URL to scrape"
          }
        },
        required: ["url"]
      }
    }
  }
];

// Function to call OpenRouter API with GPT-4o-mini
async function callOpenRouterAPI(messages, useTools = true) {
  const payload = {
    model: "openai/gpt-4o-mini", // Use GPT-4o-mini which supports tools and is cost-effective
    messages: messages,
    temperature: 0.7,
    max_tokens: 4000
  };

  if (useTools) {
    payload.tools = tools;
    payload.tool_choice = "auto";
  }

  const response = await fetch(OPENROUTER_API_URL, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'http://localhost:3000',
      'X-Title': 'AI Web Agent'
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

// Function to handle conversation with multiple rounds of function calling
async function handleConversation(initialPrompt) {
  const messages = [
    {
      role: "user",
      content: initialPrompt
    }
  ];

  let maxRounds = 8; // Prevent infinite loops
  let round = 0;

  while (round < maxRounds) {
    round++;
    console.log(`\n=== ROUND ${round} ===`);

    // Send request to OpenRouter
    const response = await callOpenRouterAPI(messages, true);

    const choice = response.choices[0];
    const message = choice.message;

    // Add assistant response to conversation history
    messages.push({
      role: "assistant",
      content: message.content,
      tool_calls: message.tool_calls
    });

    // Check if there are function calls
    if (message.tool_calls && message.tool_calls.length > 0) {
      console.log(`Function calls detected: ${message.tool_calls.length}`);

      for (const toolCall of message.tool_calls) {
        console.log(`Executing function: ${toolCall.function.name}`);
        console.log(`Parameters:`, toolCall.function.arguments);

        let result;
        try {
          const args = JSON.parse(toolCall.function.arguments);

          if (toolCall.function.name === 'search_web') {
            result = await performSerperSearch(args);
          } else if (toolCall.function.name === 'scrape_webpage') {
            result = await performSerperScrape(args);
          } else {
            throw new Error(`Unknown function: ${toolCall.function.name}`);
          }

          // Add function result to conversation history
          messages.push({
            role: "tool",
            content: JSON.stringify(result),
            tool_call_id: toolCall.id
          });
        } catch (error) {
          console.error(`Error executing ${toolCall.function.name}:`, error.message);

          // Add error result to conversation history
          messages.push({
            role: "tool",
            content: JSON.stringify({ error: error.message }),
            tool_call_id: toolCall.id
          });
        }
      }
    } else {
      // No function calls, we have the final response
      console.log("\n=== FINAL RESPONSE ===");
      console.log(message.content);
      break;
    }
  }

  if (round >= maxRounds) {
    console.log("\n=== MAX ROUNDS REACHED ===");
    console.log("Getting final summary...");

    // Force a final summary without function calling
    messages.push({
      role: "user",
      content: "Based on all the information you've gathered, please provide a comprehensive summary to the original query."
    });

    const finalResponse = await callOpenRouterAPI(messages, false);
    const finalMessage = finalResponse.choices[0].message;

    console.log("\n=== FINAL SUMMARY ===");
    console.log(finalMessage.content);
  }
}

// Run the conversation
await handleConversation(
  `
  Anderson Craft Ales	1030 Elias St, London, ON N5W 3P6	(548) 888-2537	London
Labatt Brewery	150 Simcoe St, London, ON N6A 4M3	(519) 850-8687	London
Equals Brewing Company Inc	695 Sovereign Rd, London, ON N5V 4K8	1 877-378-2571	London



above given  are companies in canada ,I need to find the following personnel of those companies

using serper - search mcp conduct seperate search queries  to find the person in each of  the below postions  , their contact number and their personal email
(1) Owner, President, CEO, MD,
(2) Director Finance, VP- Finance, Chief Financial Officer, Financial Controller
(3) Chief operating office, Vice President - Operation.


always state the persons position inside brackets with name.search deeply for above people in various sites until the information is found.If people in above ositions are not found after a deep search find a person in any other position with the position in the company.
For contact 3 phone always give the company phone number
Do not miss any of the companies given.
all companies have their official websites use scrape and find the needed information  in the website , also search linked in if the info is not found first, search vigorously until the data is found,if no email or number is found mention the linked in profile link in other details ,always give a small summary on the size of the firm and their area of specialization by 6-7 words.make sure the information is very very accurate,never provide unrelated information.If the individuals phone numbers are not found or not certain just give a company phone number as contact 3 phone.give the info in a json array. below is the example format ,always stick to this format.dont change the original order of companoies give the json array in the same order and never exclude any company given in the above list always search for every company.
always follow the order given above when giving the output  {
    "firmName": "",
    "address": "",
    “contact 1 name”:””,
    “contact 1 phone": "",
    “contact 1 email”: "",
    “contact 2 name”:””,
    “contact 2 phone": "",
    “contact 2 email: "",
     “contact 3 name”:””,
    “contact 3 phone": "",
    “contact 3 email”: "",

    "website": "",
    “company email": “”,
     “otherDetails”: “”
  },







  `
);
