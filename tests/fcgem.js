import { GoogleGenAI } from '@google/genai';

// ============================================================================
// CONFIGURATION VARIABLES
// ============================================================================

// API Keys
const SERPER_API_KEY = "03a6ce71d56b6d0336aefb638405090343bca125";
const GEMINI_API_KEY = "AIzaSyBaDWf_3ly9xZM26kYM3qppNpbOqwuiaTE";

// Default Search Parameters
const SEARCH_DEFAULTS = {
  NUM_RESULTS: 10,                // Default number of search results
  REGION: 'us',                   // Default region code
  LANGUAGE: 'en'                  // Default language code
};



// ============================================================================
// FUNCTION DECLARATIONS
// ============================================================================

// Define function declarations for search and scrape
const functionDeclarations = [
  {
    name: "search_web",
    description: "Search the web using Serper API to get current information",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query"
        },
        num: {
          type: "number",
          description: "Number of results to return (default: 10)"
        },
        gl: {
          type: "string",
          description: "Region code (default: 'us')"
        },
        hl: {
          type: "string",
          description: "Language code (default: 'en')"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "scrape_webpage",
    description: "Scrape content from a specific webpage URL",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to scrape"
        }
      },
      required: ["url"]
    }
  }
];

// ============================================================================
// IMPLEMENTATION FUNCTIONS
// ============================================================================

// Function to perform web search using Serper API
async function performSerperSearch({
  query,
  num = SEARCH_DEFAULTS.NUM_RESULTS,
  gl = SEARCH_DEFAULTS.REGION,
  hl = SEARCH_DEFAULTS.LANGUAGE
}) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API
async function performSerperScrape({ url }) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}



// ============================================================================
// CONFIGURATION
// ============================================================================

const config = {
    tools: [{
        functionDeclarations: functionDeclarations
    }],
    // Force the model to call 'any' function, instead of chatting.
    toolConfig: {
        functionCallingConfig: {
        mode: 'any'
        }
    }
};

// Configure the client
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });


// ============================================================================
// SIMPLE EXECUTION HELPER
// ============================================================================

// Simple function to log function results



// ============================================================================
// SIMPLE SUMMARY GENERATION
// ============================================================================

async function generateSimpleSummary(functionResults, chat) {
    try {
        console.log("\n" + "=".repeat(50));
        console.log("GENERATING FINAL SUMMARY");
        console.log("=".repeat(50));

        // Prepare the summary prompt with all function results
        let summaryPrompt = "Based on the following function call results, please provide a comprehensive summary:\n\n";

        

        summaryPrompt += `
INSTRUCTIONS:
Please provide a comprehensive response to the user's original query based on all the data collected above.
- Organize information logically and clearly
- Include specific details and findings from the gathered information
- Ensure accuracy by using only the information collected during the search/scrape process
- Make the response complete and actionable for the user's needs`;

        // Disable function calling for summary generation
        const originalConfig = chat.config;
        chat.config = {
            tools: [{
                functionDeclarations: functionDeclarations
            }],
            toolConfig: {
                functionCallingConfig: {
                    mode: 'NONE'
                }
            }
        };

        console.log("Requesting AI summary...");
        const summaryResponse = await chat.sendMessage({
            message: summaryPrompt
        });

        // Restore original configuration
        chat.config = originalConfig;

        console.log("\n" + "=".repeat(50));
        console.log("FINAL SUMMARY");
        console.log("=".repeat(50));
        console.log(summaryResponse.text);
        console.log("=".repeat(50));

    } catch (error) {
        console.error("Error generating summary:", error);

        // Fallback: provide a basic summary
        console.log("\n" + "=".repeat(50));
        console.log("BASIC SUMMARY (AI summary failed)");
        console.log("=".repeat(50));

        
    }
}



// ============================================================================
// SIMPLE QUERY EXECUTION
// ============================================================================

// Function to run simple one-time search with a specific query
async function runSimpleSearchWithQuery(query) {
    try {
        // Create a chat session
        const chat = ai.chats.create({
            model: 'gemini-2.0-flash',
            config: config
        });

        console.log("Simple Search and Scrape System:");
        console.log("=================================");
        console.log(`Query: ${query}`);
        console.log("=================================");

        const allFunctionResults = [];

        // Send the query to the model
        const message = `User Query: ${query}

INSTRUCTIONS:
Please analyze the user's query and perform the necessary search and scrape operations to gather comprehensive information.
You can use the available functions (search_web and scrape_webpage) to collect all the required data.
Focus on providing complete and accurate information for the user's request.`;

        console.log("Sending query to AI model...");
        let response = await chat.sendMessage({
            message: message
        });

        let shouldContinue = true;
        let evaluationResponse;
   

        while(shouldContinue){

        if (response.functionCalls && response.functionCalls.length > 0) {
            console.log(`AI requested ${response.functionCalls.length} function calls`);

            for (const fn of response.functionCalls) {
                const args = Object.entries(fn.args)
                    .map(([key, val]) => `${key}=${val}`)
                    .join(', ');
                console.log(`Executing: ${fn.name}(${args})`);

                // Execute the function calls and collect results
                let result;
                try {
                    if (fn.name === 'search_web') {
                        result = await performSerperSearch(fn.args);
                        console.log(`\x1b[32mSearch completed: ${JSON.stringify(result, null, 2)} \x1b[0m`);

                        console.log(`Search completed: Found ${result.organic?.length || 0} results`);

                        const funcResult = {
                            functionName: fn.name,
                            args: fn.args,
                            result: result,
                            success: true
                        };

                        allFunctionResults.push(funcResult);

                    } else if (fn.name === 'scrape_webpage') {
                        result = await performSerperScrape(fn.args);
                        //log result in green
                        console.log(`\x1b[32mScrape completed: ${JSON.stringify(result, null, 2)} \x1b[0m`);
                        console.log(`Scrape completed: ${result.text?.length || 0} characters extracted`);

                        const funcResult = {
                            functionName: fn.name,
                            args: fn.args,
                            result: result,
                            success: true
                        };

                        allFunctionResults.push(funcResult);

                    }
                } catch (error) {
                    console.error(`Error executing ${fn.name}:`, error.message);

                    const funcResult = {
                        functionName: fn.name,
                        args: fn.args,
                        error: error.message,
                        success: false
                    };

                    allFunctionResults.push(funcResult);
                }
            }

            console.log(`Completed ${allFunctionResults.length} operations`);
            console.log(`allFunctionResults:${JSON.stringify(allFunctionResults, null, 2)}`);

        } else {
            console.log("No function calls were made");
        }

        //send the results back to the chat with a prompt to that asks for a should continue parameter in json
        const functionResultsText = allFunctionResults.map((funcResult, index) => {
            return `${index}. ${JSON.stringify(funcResult, null, 2)}`;
        }).join('\n\n');
        const evaluationPrompt = `
        Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

        Consider:
        1. The original query and its context
        2. The information gathered from previous tool calls
        3. The relevance and comprehensiveness of the gathered information

        Provide your evaluation in the following JSON format:
        {
            "shouldContinue": true/false,
            "reasoning": "Brief explanation of why more rounds are or aren't needed",
            "confidence": 1-10 (how confident you are in this decision)
        }

        Function call results:\n\n${functionResultsText}
        `;
        console.log(`\n${"=".repeat(50)}`);
        console.log("Evaluating if more rounds are needed");
        console.log(`${"=".repeat(50)}`);

        // Temporarily disable function calling for evaluation
        const originalConfig = chat.config;
        chat.config = {
            tools: [{
                functionDeclarations: functionDeclarations
            }],
            toolConfig: {
                functionCallingConfig: {
                    mode: 'NONE'
                }
            }
        };

        evaluationResponse = await chat.sendMessage({
            message: evaluationPrompt
        });

        // Restore original configuration
        chat.config = originalConfig;
        // get the should continue parameter from response
        let evaluationText = evaluationResponse.text || '';
        if (!evaluationText) {
            console.log("No text response from evaluation, stopping...");
            shouldContinue = false;
        } else {
            try {
                let evaluation = JSON.parse(evaluationText.replace(/```json\n?|\n?```/g, ''));
                shouldContinue = evaluation.shouldContinue;
            } catch (parseError) {
                console.log("Failed to parse evaluation response, stopping...");
                shouldContinue = false;
            }
        }

        //exit loop if false
        if (!shouldContinue) {
            break;
        }

        





        // send a message to chat to continue search and scrape for more to gather more information
        const continuePrompt = `
        Based on these results collected:${functionResultsText}
        
        ,continue searching for more areas to find answers to the users original query,
        search the un explored areas for more data.

        Consider:
        1. The original query and its context
        2. The information gathered from previous tool calls
        3. The relevance and comprehensiveness of the gathered information

        `
        response = await chat.sendMessage({
            message: continuePrompt
        });



        



        }

       

        console.log(`\n${"=".repeat(50)}`);
        console.log("Evaluation Response");
        console.log(`${"=".repeat(50)}`);
        if (evaluationResponse && evaluationResponse.text) {
            console.log(evaluationResponse.text);
        } else {
            console.log("No evaluation response text available");
        }
        console.log(`\n${"=".repeat(50)}`);
        

        // Generate final summary based on all function results
        console.log(`\n${"=".repeat(50)}`);
        console.log("Generating Final Summary");
        console.log(`${"=".repeat(50)}`);

        if (allFunctionResults.length > 0) {
            await generateSimpleSummary(allFunctionResults, chat);
        } else {
            console.log("No results to summarize");
        }

    } catch (error) {
        console.error("Error in simple search:", error);
    }
}

const query = `

PepsiCo Foods Canada 1001 Bishop St N, Cambridge, ON N3H 4V8 (519) 653-5721 Cambridge Port Pop Inc 85 Saltsman Dr, Cambridge, ON N3H 4R7 1 888-376-8706 Cambridge Snackruptors Inc. 50 High Ridge Ct, Cambridge, ON N1R 7L3 (519) 623-4964 Cambridge above given are food manufacturing companies in canada ,I need to find the following personnel of those companies using serper - search mcp conduct seperate search queries to find the person in each of the below postions , their contact number and their personal email (1) Owner, President, CEO, MD, (2) Director Finance, VP- Finance, Chief Financial Officer, Financial Controller (3) Chief operating office, Vice President - Operation. always state the persons position inside brackets with name.search deeply for above people in various sites until the information is found.If people in above ositions are not found after a deep search find a person in any other position with the position in the company.If Do not miss any of the companies given. all companies have their official websites use scrape and find the needed information in the website , also search linked in if the info is not found first, search vigorously until the data is found,if no email or number is found mention the linked in profile link in other details ,always give a small summary on the size of the firm and their area of specialization by 6-7 words.make sure the information is very very accurate,never provide unrelated information .give the info in a json array. below is the example format ,always stick to this format.dont change the original order of companoies give the json array in the same order and never exclude any company given in the above list always search for every company. always follow the order given above when giving the output  { "firmName": "", "address": "", “contact 1 name”:””, “contact 1 phone": "", “contact 1 email”: "", “contact 2 name”:””, “contact 2 phone": "", “contact 2 email: "", “contact 3 name”:””, “contact 3 phone": "", “contact 3 email”: "", "website": "", “company email": “”, “otherDetails”: “” },



`;

if (!query) {
    console.log("❌ Error: No search query provided");
    console.log("\n📋 Usage: node fcgem.js \"Your search query here\"");
    console.log("\n💡 Examples:");
    console.log("   node fcgem.js \"Latest developments in renewable energy\"");
    console.log("   node fcgem.js \"Best restaurants in Tokyo 2024\"");
    console.log("   node fcgem.js \"Climate change impact on agriculture\"");
    console.log("   node fcgem.js \"Recent breakthroughs in cancer research\"");
    console.log("   node fcgem.js \"Cryptocurrency market trends 2024\"");
    console.log("\n🔍 The system will perform a simple one-time search and scraping for your query.");
    process.exit(1);
} else {
    runSimpleSearchWithQuery(query);
}