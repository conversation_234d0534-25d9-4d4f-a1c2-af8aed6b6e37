import { GoogleGenAI } from '@google/genai';
import readline from 'readline';

// Serper API configuration
const SERPER_API_KEY = "5e5eba5e509e26a2ca11b831913e3bd5aeabb459";

// Function to perform web search using Serper API
async function performSerperSearch({ query, num = 10, gl = 'us', hl = 'en' }) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API
async function performSerperScrape({ url }) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}

// Configure the AI client
const ai = new GoogleGenAI({ apiKey: "AIzaSyCgsPrZ70Vo_sVH0_bPq0rb_BYl83CHnuE" });

// Define function declarations for Gemini
const functionDeclarations = [
  {
    name: "search_web",
    description: "Search the web using Serper API to get current information",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query"
        },
        num: {
          type: "number",
          description: "Number of results to return (default: 10)"
        },
        gl: {
          type: "string",
          description: "Region code (default: 'us')"
        },
        hl: {
          type: "string",
          description: "Language code (default: 'en')"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "scrape_webpage",
    description: "Scrape content from a specific webpage URL",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to scrape"
        }
      },
      required: ["url"]
    }
  }
];

// Function to evaluate if more rounds are needed using AI
async function shouldContinueRounds(conversationHistory, round) {
  // Safety mechanism: absolute maximum to prevent infinite loops
  const ABSOLUTE_MAX_ROUNDS = 20;

  if (round >= ABSOLUTE_MAX_ROUNDS) {
    console.log(`Reached absolute maximum of ${ABSOLUTE_MAX_ROUNDS} rounds - stopping for safety`);
    return false;
  }

  // For the first few rounds, always continue if there are function calls
  if (round <= 3) {
    return true;
  }

  // Ask the AI model to evaluate if more rounds are needed
  try {
    const evaluationPrompt = `
Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

Consider:
1. Have we gathered sufficient information to provide a comprehensive answer?
2. Are there any gaps in the information that require additional searches or scraping?
3. Is the current information accurate and complete enough?
4. Would additional tool calls significantly improve the response quality?

Respond with a JSON object containing:
{
  "shouldContinue": true/false,
  "reasoning": "Brief explanation of why more rounds are or aren't needed",
  "confidence": 1-10 (how confident you are in this decision)
}

Current round: ${round}
`;

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [
        ...conversationHistory,
        {
          role: "user",
          parts: [{ text: evaluationPrompt }]
        }
      ],
    });

    const responseText = response.text || "{}";
    const evaluation = JSON.parse(responseText.replace(/```json\n?|\n?```/g, ''));

    console.log(`\n=== AI EVALUATION (Round ${round}) ===`);
    console.log(`Should continue: ${evaluation.shouldContinue}`);
    console.log(`Reasoning: ${evaluation.reasoning}`);
    console.log(`Confidence: ${evaluation.confidence}/10`);

    return evaluation.shouldContinue && evaluation.confidence >= 6;
  } catch (error) {
    console.error("Error in AI evaluation:", error);
    // Fallback: continue if we're under a reasonable limit
    return round < 8;
  }
}

// Function to handle conversation with multiple rounds of function calling
async function handleConversation(userPrompt, conversationHistory = []) {
  // Add the new user prompt to conversation history
  conversationHistory.push({
    role: "user",
    parts: [{ text: userPrompt }]
  });

  let round = 0;
  let shouldContinue = true;

  while (shouldContinue) {
    round++;
    console.log(`\n=== ROUND ${round} ===`);

    // Send request to the model
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: conversationHistory,
      config: {
        tools: [{ functionDeclarations }],
        toolConfig: {
          functionCallingConfig: {
            mode: 'any'
          }
        }
      },
    });

    // Add model response to conversation history
    conversationHistory.push({
      role: "model",
      parts: response.candidates[0].content.parts
    });

    // Check if there are function calls
    if (response.functionCalls && response.functionCalls.length > 0) {
      console.log(`Function calls detected: ${response.functionCalls.length}`);

      const functionResults = [];

      for (const functionCall of response.functionCalls) {
        console.log(`Executing function: ${functionCall.name}`);
        console.log(`Parameters:`, functionCall.args);

        let result;
        try {
          if (functionCall.name === 'search_web') {
            result = await performSerperSearch(functionCall.args);
          } else if (functionCall.name === 'scrape_webpage') {
            result = await performSerperScrape(functionCall.args);
          } else {
            throw new Error(`Unknown function: ${functionCall.name}`);
          }

          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: result
            }
          });
        } catch (error) {
          console.error(`Error executing ${functionCall.name}:`, error.message);
          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: { error: error.message }
            }
          });
        }
      }

      // Add function results to conversation history
      conversationHistory.push({
        role: "function",
        parts: functionResults
      });

      // Use AI to decide if more rounds are needed
      shouldContinue = await shouldContinueRounds(conversationHistory, round);

      if (!shouldContinue) {
        console.log("\n=== AI DECIDED TO STOP ===");
        console.log("AI determined sufficient information has been gathered. Getting final response...");

        // Get final response without function calling
        conversationHistory.push({
          role: "user",
          parts: [{ text: "Based on all the information you've gathered, please provide a comprehensive final response to the original query." }]
        });

        const finalResponse = await ai.models.generateContent({
          model: "gemini-2.0-flash",
          contents: conversationHistory,
        });

        console.log("\n=== FINAL RESPONSE ===");
        console.log(finalResponse.text);
        break;
      }
    } else {
      // No function calls, we have the final response
      console.log("\n=== FINAL RESPONSE ===");
      console.log(response.text);
      shouldContinue = false;
    }
  }

  // This should rarely be reached now since AI decides when to stop
  if (round >= 20) {
    console.log("\n=== SAFETY LIMIT REACHED ===");
    console.log("Reached absolute safety limit. Providing final summary...");

    conversationHistory.push({
      role: "user",
      parts: [{ text: "Based on all the information you've gathered, please provide a comprehensive summary to the original query." }]
    });

    const finalResponse = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: conversationHistory,
    });

    console.log("\n=== FINAL SUMMARY ===");
    console.log(finalResponse.text);
  }

  // Return the updated conversation history for continuous chat
  return conversationHistory;
}

// Create readline interface for continuous input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: '\n🤖 You: '
});

// Main chat loop function
async function startContinuousChat() {
  let conversationHistory = [];

  console.log('\n=== AI Web Agent Chat ===');
  console.log('Type your questions and I\'ll search the web to help you!');
  console.log('Commands: /exit (quit), /clear (clear history), /history (show conversation)');
  console.log('=========================================\n');

  rl.prompt();

  rl.on('line', async (input) => {
    const userInput = input.trim();

    // Handle special commands
    if (userInput === '/exit') {
      console.log('\n👋 Goodbye!');
      rl.close();
      return;
    }

    if (userInput === '/clear') {
      conversationHistory = [];
      console.log('\n🧹 Conversation history cleared!');
      rl.prompt();
      return;
    }

    if (userInput === '/history') {
      console.log('\n📜 Conversation History:');
      console.log('========================');
      conversationHistory.forEach((entry, index) => {
        if (entry.role === 'user') {
          console.log(`${index + 1}. User: ${entry.parts[0].text.substring(0, 100)}...`);
        } else if (entry.role === 'model') {
          console.log(`${index + 1}. AI: [Response with ${entry.parts.length} parts]`);
        }
      });
      console.log('========================\n');
      rl.prompt();
      return;
    }

    if (userInput === '') {
      rl.prompt();
      return;
    }

    try {
      console.log('\n🔍 Processing your request...\n');

      // Handle the conversation and get updated history
      conversationHistory = await handleConversation(userInput, conversationHistory);

      console.log('\n' + '='.repeat(50));

    } catch (error) {
      console.error('\n❌ Error:', error.message);
    }

    rl.prompt();
  });

  rl.on('close', () => {
    console.log('\n👋 Chat session ended.');
    process.exit(0);
  });
}

// Start the continuous chat
startContinuousChat().catch(console.error);
