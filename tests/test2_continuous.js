import readline from 'readline';

// OpenRouter API configuration
const OPENROUTER_API_KEY = "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";
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

// Serper API configuration
const SERPER_API_KEY = "5e5eba5e509e26a2ca11b831913e3bd5aeabb459";

// Function to perform web search using Serper API
async function performSerperSearch({ query, num = 10, gl = 'us', hl = 'en' }) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API
async function performSerperScrape({ url }) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}

// Define function declarations for OpenRouter (OpenAI format)
const tools = [
  {
    type: "function",
    function: {
      name: "search_web",
      description: "Search the web using Serper API to get current information",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query"
          },
          num: {
            type: "number",
            description: "Number of results to return (default: 10)"
          },
          gl: {
            type: "string",
            description: "Region code (default: 'us')"
          },
          hl: {
            type: "string",
            description: "Language code (default: 'en')"
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "scrape_webpage",
      description: "Scrape content from a specific webpage URL",
      parameters: {
        type: "object",
        properties: {
          url: {
            type: "string",
            description: "The URL to scrape"
          }
        },
        required: ["url"]
      }
    }
  }
];

// Function to call OpenRouter API with GPT-4o-mini
async function callOpenRouterAPI(messages, useTools = true) {
  const payload = {
    model: "openai/gpt-4o-mini", // Use GPT-4o-mini which supports tools and is cost-effective
    messages: messages,
    temperature: 0.7,
    max_tokens: 4000
  };

  if (useTools) {
    payload.tools = tools;
    payload.tool_choice = "auto";
  }

  const response = await fetch(OPENROUTER_API_URL, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'http://localhost:3000',
      'X-Title': 'AI Web Agent'
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

// Function to evaluate if more rounds are needed using AI
async function shouldContinueRounds(messages, round) {
  // Safety mechanism: absolute maximum to prevent infinite loops
  const ABSOLUTE_MAX_ROUNDS = 20;

  if (round >= ABSOLUTE_MAX_ROUNDS) {
    console.log(`Reached absolute maximum of ${ABSOLUTE_MAX_ROUNDS} rounds - stopping for safety`);
    return false;
  }

  // For the first few rounds, always continue if there are function calls
  if (round <= 3) {
    return true;
  }

  // Ask the AI model to evaluate if more rounds are needed
  try {
    const evaluationPrompt = `
Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

Consider:
1. Have we gathered sufficient information to provide a comprehensive answer?
2. Are there any gaps in the information that require additional searches or scraping?
3. Is the current information accurate and complete enough?
4. Would additional tool calls significantly improve the response quality?

Respond with a JSON object containing:
{
  "shouldContinue": true/false,
  "reasoning": "Brief explanation of why more rounds are or aren't needed",
  "confidence": 1-10 (how confident you are in this decision)
}

Current round: ${round}
`;

    const evaluationMessages = [
      ...messages,
      {
        role: "user",
        content: evaluationPrompt
      }
    ];

    const response = await callOpenRouterAPI(evaluationMessages, false);
    const responseText = response.choices[0].message.content || "{}";
    const evaluation = JSON.parse(responseText.replace(/```json\n?|\n?```/g, ''));

    console.log(`\n=== AI EVALUATION (Round ${round}) ===`);
    console.log(`Should continue: ${evaluation.shouldContinue}`);
    console.log(`Reasoning: ${evaluation.reasoning}`);
    console.log(`Confidence: ${evaluation.confidence}/10`);

    return evaluation.shouldContinue && evaluation.confidence >= 6;
  } catch (error) {
    console.error("Error in AI evaluation:", error);
    // Fallback: continue if we're under a reasonable limit
    return round < 8;
  }
}

// Function to handle conversation with multiple rounds of function calling
async function handleConversation(userPrompt, messages = []) {
  // Add the new user prompt to conversation history
  messages.push({
    role: "user",
    content: userPrompt
  });

  let round = 0;
  let shouldContinue = true;

  while (shouldContinue) {
    round++;
    console.log(`\n=== ROUND ${round} ===`);

    // Send request to OpenRouter
    const response = await callOpenRouterAPI(messages, true);

    const choice = response.choices[0];
    const message = choice.message;

    // Add assistant response to conversation history
    messages.push({
      role: "assistant",
      content: message.content,
      tool_calls: message.tool_calls
    });

    // Check if there are function calls
    if (message.tool_calls && message.tool_calls.length > 0) {
      console.log(`Function calls detected: ${message.tool_calls.length}`);

      for (const toolCall of message.tool_calls) {
        console.log(`Executing function: ${toolCall.function.name}`);
        console.log(`Parameters:`, toolCall.function.arguments);

        let result;
        try {
          const args = JSON.parse(toolCall.function.arguments);

          if (toolCall.function.name === 'search_web') {
            result = await performSerperSearch(args);
          } else if (toolCall.function.name === 'scrape_webpage') {
            result = await performSerperScrape(args);
          } else {
            throw new Error(`Unknown function: ${toolCall.function.name}`);
          }

          // Add function result to conversation history
          messages.push({
            role: "tool",
            content: JSON.stringify(result),
            tool_call_id: toolCall.id
          });
        } catch (error) {
          console.error(`Error executing ${toolCall.function.name}:`, error.message);

          // Add error result to conversation history
          messages.push({
            role: "tool",
            content: JSON.stringify({ error: error.message }),
            tool_call_id: toolCall.id
          });
        }
      }

      // Use AI to decide if more rounds are needed
      shouldContinue = await shouldContinueRounds(messages, round);

      if (!shouldContinue) {
        console.log("\n=== AI DECIDED TO STOP ===");
        console.log("AI determined sufficient information has been gathered. Getting final response...");

        // Get final response without function calling
        messages.push({
          role: "user",
          content: "Based on all the information you've gathered, please provide a comprehensive final response to the original query."
        });

        const finalResponse = await callOpenRouterAPI(messages, false);
        const finalMessage = finalResponse.choices[0].message;

        console.log("\n=== FINAL RESPONSE ===");
        console.log(finalMessage.content);
        break;
      }
    } else {
      // No function calls, we have the final response
      console.log("\n=== FINAL RESPONSE ===");
      console.log(message.content);
      shouldContinue = false;
    }
  }

  // This should rarely be reached now since AI decides when to stop
  if (round >= 20) {
    console.log("\n=== SAFETY LIMIT REACHED ===");
    console.log("Reached absolute safety limit. Providing final summary...");

    messages.push({
      role: "user",
      content: "Based on all the information you've gathered, please provide a comprehensive summary to the original query."
    });

    const finalResponse = await callOpenRouterAPI(messages, false);
    const finalMessage = finalResponse.choices[0].message;

    console.log("\n=== FINAL SUMMARY ===");
    console.log(finalMessage.content);
  }

  // Return the updated conversation history for continuous chat
  return messages;
}

// Create readline interface for continuous input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: '\n🤖 You: '
});

// Main chat loop function
async function startContinuousChat() {
  let messages = [];

  console.log('\n=== AI Web Agent Chat (OpenRouter) ===');
  console.log('Type your questions and I\'ll search the web to help you!');
  console.log('Commands: /exit (quit), /clear (clear history), /history (show conversation)');
  console.log('=========================================\n');

  rl.prompt();

  rl.on('line', async (input) => {
    const userInput = input.trim();

    // Handle special commands
    if (userInput === '/exit') {
      console.log('\n👋 Goodbye!');
      rl.close();
      return;
    }

    if (userInput === '/clear') {
      messages = [];
      console.log('\n🧹 Conversation history cleared!');
      rl.prompt();
      return;
    }

    if (userInput === '/history') {
      console.log('\n📜 Conversation History:');
      console.log('========================');
      messages.forEach((entry, index) => {
        if (entry.role === 'user') {
          console.log(`${index + 1}. User: ${entry.content.substring(0, 100)}...`);
        } else if (entry.role === 'assistant') {
          console.log(`${index + 1}. AI: ${entry.content ? entry.content.substring(0, 100) + '...' : '[Tool calls]'}`);
        } else if (entry.role === 'tool') {
          console.log(`${index + 1}. Tool: [Function result]`);
        }
      });
      console.log('========================\n');
      rl.prompt();
      return;
    }

    if (userInput === '') {
      rl.prompt();
      return;
    }

    try {
      console.log('\n🔍 Processing your request...\n');

      // Handle the conversation and get updated history
      messages = await handleConversation(userInput, messages);

      console.log('\n' + '='.repeat(50));

    } catch (error) {
      console.error('\n❌ Error:', error.message);
    }

    rl.prompt();
  });

  rl.on('close', () => {
    console.log('\n👋 Chat session ended.');
    process.exit(0);
  });
}

// Start the continuous chat
startContinuousChat().catch(console.error);
