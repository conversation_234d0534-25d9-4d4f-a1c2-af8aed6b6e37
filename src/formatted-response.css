/* Enhanced formatting for AI responses - Quasari theme matching reference images */
.formatted-response {
  line-height: 1.6;
  color: #1A1A1A;
  font-weight: 400;
}

.formatted-response h2 {
  border-bottom: 4px solid #F4E04D;
  padding-bottom: 1rem;
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
  color: #1A1A1A;
  font-size: 1.5rem;
}

.formatted-response h3 {
  margin-top: 2rem;
  margin-bottom: 1.25rem;
  font-weight: 700;
  color: #1A1A1A;
  font-size: 1.25rem;
}

.formatted-response p {
  margin-bottom: 1.5rem;
  text-align: left;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.7;
}

.formatted-response ul,
.formatted-response ol {
  margin-bottom: 1.5rem;
  padding-left: 2.5rem;
}

.formatted-response li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  font-size: 1.125rem;
}

.formatted-response a {
  color: #E8C93A;
  text-decoration: underline;
  font-weight: 600;
  transition: color 0.2s ease;
}

.formatted-response a:hover {
  color: #D8A928;
}

.formatted-response strong {
  color: #1A1A1A;
  font-weight: 700;
}

.formatted-response em {
  color: #6D6D6D;
  font-style: italic;
  font-weight: 500;
}

/* Code blocks if any */
.formatted-response code {
  background-color: #F5F5F5;
  color: #1A1A1A;
  padding: 0.375rem 0.75rem;
  border-radius: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 1rem;
  border: 1px solid #E5E5E5;
  font-weight: 500;
}

/* Blockquotes if any */
.formatted-response blockquote {
  border-left: 6px solid #F4E04D;
  background-color: #FFFEF7;
  padding: 1.5rem 2rem;
  margin: 2rem 0;
  font-style: italic;
  color: #6D6D6D;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 500;
}

/* JSON and Code Block Styles */
.formatted-response .json-block,
.formatted-response .code-block {
  margin: 1.5rem 0;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.formatted-response .json-block:hover,
.formatted-response .code-block:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.formatted-response .json-block {
  border-color: #10B981;
  background: linear-gradient(135deg, #ECFDF5 0%, #F0FDF4 100%);
}

.formatted-response .json-header,
.formatted-response .code-header {
  background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px 16px 0 0;
  margin: -1px -1px 0 -1px;
}

.formatted-response .json-header {
  background: linear-gradient(135deg, #059669 0%, #10B981 100%);
}

.formatted-response .json-content,
.formatted-response .code-content {
  padding: 1.5rem;
  margin: 0;
  background: transparent;
  border: none;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.7;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-feature-settings: "liga" 1, "calt" 1;
  position: relative;
}

.formatted-response .json-content {
  color: #064E3B;
  background: linear-gradient(135deg, #ECFDF5 0%, #F0FDF4 100%);
  border-radius: 0 0 16px 16px;
  margin-top: 0;
}

.formatted-response .code-content {
  color: #374151;
  background: #F3F4F6;
  border-radius: 0 0 16px 16px;
  margin-top: 0;
}

/* JSON Syntax Highlighting */
.formatted-response .json-content code {
  color: inherit;
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

.formatted-response .json-highlighted {
  position: relative;
  z-index: 1;
}

.formatted-response .json-key {
  color: #0369A1;
  font-weight: 600;
}

.formatted-response .json-string {
  color: #059669;
  font-weight: 500;
}

.formatted-response .json-number {
  color: #DC2626;
  font-weight: 600;
}

.formatted-response .json-boolean {
  color: #7C3AED;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.85em;
}

.formatted-response .json-null {
  color: #6B7280;
  font-weight: 600;
  font-style: italic;
}

.formatted-response .json-bracket {
  color: #374151;
  font-weight: 700;
  font-size: 1.1em;
}

/* Enhanced JSON syntax highlighting */
.formatted-response .json-content {
  position: relative;
}

.formatted-response .json-content::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.1) 100%);
  pointer-events: none;
}

/* Copy button for JSON blocks */
.formatted-response .copy-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  cursor: pointer;
  font-size: 0.65rem;
  color: white;
  text-transform: none;
  letter-spacing: normal;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.375rem;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  overflow: hidden;
  z-index: 10;
}

.formatted-response .copy-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.formatted-response .copy-button:hover::before {
  left: 100%;
}

.formatted-response .copy-button:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.formatted-response .copy-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Scrollbar styling for code blocks */
.formatted-response .json-content::-webkit-scrollbar,
.formatted-response .code-content::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.formatted-response .json-content::-webkit-scrollbar-track,
.formatted-response .code-content::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 4px;
}

.formatted-response .json-content::-webkit-scrollbar-thumb,
.formatted-response .code-content::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 4px;
}

.formatted-response .json-content::-webkit-scrollbar-thumb:hover,
.formatted-response .code-content::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
