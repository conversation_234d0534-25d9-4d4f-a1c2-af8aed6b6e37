"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState } from "react";
import { toast } from "sonner";

export function SignInForm() {
  const { signIn } = useAuthActions();
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [submitting, setSubmitting] = useState(false);

  return (
    <div className="w-full space-y-6">
      <form
        className="flex flex-col space-y-4"
        onSubmit={(e) => {
          e.preventDefault();
          setSubmitting(true);
          const formData = new FormData(e.target as HTMLFormElement);
          formData.set("flow", flow);
          void signIn("password", formData).catch((error) => {
            let toastTitle = "";
            if (error.message.includes("Invalid password")) {
              toastTitle = "Invalid password. Please try again.";
            } else {
              toastTitle =
                flow === "signIn"
                  ? "Could not sign in, did you mean to sign up?"
                  : "Could not sign up, did you mean to sign in?";
            }
            toast.error(toastTitle);
            setSubmitting(false);
          });
        }}
      >
        <input
          className="auth-input-field"
          type="email"
          name="email"
          placeholder="Email"
          required
        />
        <input
          className="auth-input-field"
          type="password"
          name="password"
          placeholder="Password"
          required
        />
        <button
          className="w-full px-6 py-4 text-white font-medium transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed text-lg mt-2"
          style={{
            backgroundColor: '#000000',
            borderRadius: '32px',
            border: 'none',
            letterSpacing: '-0.01em'
          }}
          type="submit"
          disabled={submitting}
        >
          {flow === "signIn" ? "Sign in" : "Sign up"}
        </button>
      </form>

      <div className="text-center text-base" style={{ color: '#6F5A3E' }}>
        <span>
          {flow === "signIn"
            ? "Don't have an account? "
            : "Already have an account? "}
        </span>
        <button
          type="button"
          className="hover:underline font-medium cursor-pointer"
          style={{ color: '#3D2914' }}
          onMouseEnter={(e) => e.currentTarget.style.color = '#6F5A3E'}
          onMouseLeave={(e) => e.currentTarget.style.color = '#3D2914'}
          onClick={() => setFlow(flow === "signIn" ? "signUp" : "signIn")}
        >
          {flow === "signIn" ? "Sign up instead" : "Sign in instead"}
        </button>
      </div>

      <div className="flex items-center justify-center">
        <div className="flex-1" style={{ borderTop: '1px solid #EDE4D8' }}></div>
        <span className="px-4 font-normal text-sm" style={{ color: '#6F5A3E' }}>or</span>
        <div className="flex-1" style={{ borderTop: '1px solid #EDE4D8' }}></div>
      </div>
      <button
        className="w-full px-6 py-4 text-black font-medium transition-all duration-150 text-lg"
        style={{
          backgroundColor: '#FF9566',
          borderRadius: '32px',
          border: 'none',
          letterSpacing: '-0.01em'
        }}
        onClick={() => void signIn("anonymous")}
      >
        Sign in anonymously
      </button>
    </div>
  );
}
