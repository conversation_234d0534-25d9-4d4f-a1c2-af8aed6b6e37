/**
 * Hook for managing conversation transition states and animations
 */

import { useState, useEffect, useRef, useCallback } from 'react';

interface ConversationTransitionState {
  isTransitioning: boolean;
  isLoading: boolean;
  previousSessionId: string | null;
  newMessageIds: Set<string>;
}

interface UseConversationTransitionProps {
  selectedSessionId: string | null;
  messages: Array<{ id: string; type: string; timestamp: number }>;
  onTransitionStart?: () => void;
  onTransitionEnd?: () => void;
}

export const useConversationTransition = ({
  selectedSessionId,
  messages,
  onTransitionStart,
  onTransitionEnd,
}: UseConversationTransitionProps) => {
  const [state, setState] = useState<ConversationTransitionState>({
    isTransitioning: false,
    isLoading: false,
    previousSessionId: null,
    newMessageIds: new Set(),
  });

  const previousSessionIdRef = useRef<string | null>(null);
  const previousMessageIdsRef = useRef<Set<string>>(new Set());
  const transitionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Track session changes
  useEffect(() => {
    const hasSessionChanged = previousSessionIdRef.current !== null && 
                             previousSessionIdRef.current !== selectedSessionId;

    if (hasSessionChanged) {
      // Start transition
      setState(prev => ({
        ...prev,
        isTransitioning: true,
        isLoading: true,
        previousSessionId: previousSessionIdRef.current,
      }));

      onTransitionStart?.();

      // Clear any existing timeout
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }

      // End transition after animation duration
      transitionTimeoutRef.current = setTimeout(() => {
        setState(prev => ({
          ...prev,
          isTransitioning: false,
          isLoading: false,
        }));
        onTransitionEnd?.();
      }, 600); // Match animation duration
    }

    previousSessionIdRef.current = selectedSessionId;
  }, [selectedSessionId, onTransitionStart, onTransitionEnd]);

  // Track new messages
  useEffect(() => {
    const currentMessageIds = new Set(messages.map(m => m.id));
    const newIds = new Set<string>();

    // Find messages that weren't in the previous set
    currentMessageIds.forEach(id => {
      if (!previousMessageIdsRef.current.has(id)) {
        newIds.add(id);
      }
    });

    if (newIds.size > 0) {
      setState(prev => ({
        ...prev,
        newMessageIds: newIds,
      }));

      // Clear new message flags after animation
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          newMessageIds: new Set(),
        }));
      }, 1000);
    }

    previousMessageIdsRef.current = currentMessageIds;
  }, [messages]);

  // Check if a message is new
  const isMessageNew = useCallback((messageId: string) => {
    return state.newMessageIds.has(messageId);
  }, [state.newMessageIds]);

  // Manually trigger loading state
  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, []);

  return {
    isTransitioning: state.isTransitioning,
    isLoading: state.isLoading,
    previousSessionId: state.previousSessionId,
    isMessageNew,
    setLoading,
  };
};

/**
 * Hook for managing sidebar session animations
 */
interface UseSidebarTransitionProps {
  sessions: Array<{ _id: string; sessionId: string }>;
  selectedSessionId: string | null;
}

export const useSidebarTransition = ({
  sessions,
  selectedSessionId,
}: UseSidebarTransitionProps) => {
  const [hoveredSessionId, setHoveredSessionId] = useState<string | null>(null);
  const [animatingSessionIds, setAnimatingSessionIds] = useState<Set<string>>(new Set());

  // Handle session hover
  const handleSessionHover = useCallback((sessionId: string | null) => {
    setHoveredSessionId(sessionId);
  }, []);

  // Handle session selection animation
  const handleSessionSelect = useCallback((sessionId: string) => {
    setAnimatingSessionIds(prev => new Set(prev).add(sessionId));
    
    // Remove animation flag after animation completes
    setTimeout(() => {
      setAnimatingSessionIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(sessionId);
        return newSet;
      });
    }, 300);
  }, []);

  // Check if session is selected
  const isSessionSelected = useCallback((sessionId: string) => {
    return selectedSessionId === sessionId;
  }, [selectedSessionId]);

  // Check if session is hovered
  const isSessionHovered = useCallback((sessionId: string) => {
    return hoveredSessionId === sessionId;
  }, [hoveredSessionId]);

  // Check if session is animating
  const isSessionAnimating = useCallback((sessionId: string) => {
    return animatingSessionIds.has(sessionId);
  }, [animatingSessionIds]);

  return {
    handleSessionHover,
    handleSessionSelect,
    isSessionSelected,
    isSessionHovered,
    isSessionAnimating,
  };
};

/**
 * Hook for managing message entrance animations
 */
interface UseMessageAnimationProps {
  messageId: string;
  messageType: 'user' | 'assistant' | 'progress';
  isNew: boolean;
}

export const useMessageAnimation = ({
  messageId,
  messageType,
  isNew,
}: UseMessageAnimationProps) => {
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    if (isNew && !hasAnimated) {
      setHasAnimated(true);
    }
  }, [isNew, hasAnimated]);

  const shouldAnimate = isNew && !hasAnimated;

  return {
    shouldAnimate,
    hasAnimated,
  };
};
