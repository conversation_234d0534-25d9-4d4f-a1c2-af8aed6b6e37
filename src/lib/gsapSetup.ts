/**
 * GSAP Setup and Configuration
 * Initializes GSAP with optimal settings for the Quasari application
 */

import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { applyReducedMotionSettings } from './animations';

// Register the React hook
gsap.registerPlugin(useGSAP);

/**
 * Initialize GSAP with global settings
 */
export const initializeGSAP = () => {
  // Set global defaults
  gsap.defaults({
    duration: 0.3,
    ease: 'power2.out',
  });

  // Apply reduced motion settings if user prefers
  applyReducedMotionSettings();

  // Set up global timeline settings for better performance
  gsap.config({
    force3D: true, // Force hardware acceleration
    nullTargetWarn: false, // Disable warnings for null targets in development
  });

  // Add custom eases that match the design system
  gsap.registerEase('quasariEase', 'power2.out');
  gsap.registerEase('quasariBounce', 'back.out(1.7)');
  gsap.registerEase('quasariElastic', 'elastic.out(1, 0.3)');

  console.log('🎬 GSAP initialized with Quasari settings');
};

/**
 * Performance optimization utilities
 */
export const gsapPerformance = {
  /**
   * Batch DOM reads and writes for better performance
   */
  batchUpdates: (callback: () => void) => {
    gsap.ticker.add(callback, true);
  },

  /**
   * Kill all animations on an element
   */
  killAnimations: (target: string | Element) => {
    gsap.killTweensOf(target);
  },

  /**
   * Pause all animations globally
   */
  pauseAll: () => {
    gsap.globalTimeline.pause();
  },

  /**
   * Resume all animations globally
   */
  resumeAll: () => {
    gsap.globalTimeline.resume();
  },

  /**
   * Set global time scale (useful for debugging)
   */
  setTimeScale: (scale: number) => {
    gsap.globalTimeline.timeScale(scale);
  },

  /**
   * Get performance stats
   */
  getStats: () => {
    return {
      fps: gsap.ticker.fps,
      lagSmoothing: gsap.ticker.lagSmoothing(),
      time: gsap.globalTimeline.time(),
      totalTime: gsap.globalTimeline.totalTime(),
    };
  },
};

/**
 * Debug utilities for development
 */
export const gsapDebug = {
  /**
   * Log animation timeline
   */
  logTimeline: (timeline: gsap.core.Timeline) => {
    console.log('Timeline duration:', timeline.duration());
    console.log('Timeline progress:', timeline.progress());
    console.log('Timeline children:', timeline.getChildren());
  },

  /**
   * Highlight animated elements
   */
  highlightElements: (selector: string) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach((el) => {
      (el as HTMLElement).style.outline = '2px solid #A8E6A3';
    });
  },

  /**
   * Show animation bounds
   */
  showBounds: (target: string | Element) => {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (element) {
      const rect = element.getBoundingClientRect();
      console.log('Element bounds:', {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
      });
    }
  },
};

/**
 * Animation presets for common UI patterns
 */
export const gsapPresets = {
  /**
   * Modal entrance animation
   */
  modalEnter: (target: string | Element) => {
    const tl = gsap.timeline();
    tl.fromTo(target, 
      { opacity: 0, scale: 0.8, y: 50 },
      { opacity: 1, scale: 1, y: 0, duration: 0.4, ease: 'back.out(1.7)' }
    );
    return tl;
  },

  /**
   * Modal exit animation
   */
  modalExit: (target: string | Element) => {
    const tl = gsap.timeline();
    tl.to(target, {
      opacity: 0,
      scale: 0.8,
      y: 50,
      duration: 0.3,
      ease: 'power2.in',
    });
    return tl;
  },

  /**
   * Toast notification animation
   */
  toastEnter: (target: string | Element, direction: 'top' | 'bottom' | 'left' | 'right' = 'right') => {
    const tl = gsap.timeline();
    const initialState: gsap.TweenVars = { opacity: 0 };
    
    switch (direction) {
      case 'top':
        initialState.y = -100;
        break;
      case 'bottom':
        initialState.y = 100;
        break;
      case 'left':
        initialState.x = -100;
        break;
      case 'right':
        initialState.x = 100;
        break;
    }

    tl.fromTo(target,
      initialState,
      { opacity: 1, x: 0, y: 0, duration: 0.4, ease: 'back.out(1.7)' }
    );
    return tl;
  },

  /**
   * Card flip animation
   */
  cardFlip: (target: string | Element) => {
    const tl = gsap.timeline();
    tl.to(target, {
      rotationY: 180,
      duration: 0.6,
      ease: 'power2.inOut',
    });
    return tl;
  },

  /**
   * Loading pulse animation
   */
  loadingPulse: (target: string | Element) => {
    return gsap.to(target, {
      opacity: 0.5,
      scale: 1.05,
      duration: 1,
      ease: 'power2.inOut',
      yoyo: true,
      repeat: -1,
    });
  },

  /**
   * Text reveal animation
   */
  textReveal: (target: string | Element, stagger = 0.05) => {
    const tl = gsap.timeline();
    tl.fromTo(target,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out', stagger }
    );
    return tl;
  },

  /**
   * Page transition
   */
  pageTransition: (outTarget: string | Element, inTarget: string | Element) => {
    const tl = gsap.timeline();
    
    // Fade out current page
    tl.to(outTarget, {
      opacity: 0,
      y: -30,
      duration: 0.3,
      ease: 'power2.in',
    });
    
    // Fade in new page
    tl.fromTo(inTarget,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.4, ease: 'power2.out' },
      '-=0.1'
    );
    
    return tl;
  },
};

/**
 * Responsive animation utilities
 */
export const gsapResponsive = {
  /**
   * Get appropriate animation duration based on screen size
   */
  getDuration: (base: number = 0.3) => {
    const isMobile = window.innerWidth < 768;
    return isMobile ? base * 0.8 : base; // Slightly faster on mobile
  },

  /**
   * Get appropriate stagger amount based on screen size
   */
  getStagger: (base: number = 0.1) => {
    const isMobile = window.innerWidth < 768;
    return isMobile ? base * 0.5 : base; // Less stagger on mobile
  },

  /**
   * Check if device prefers reduced motion
   */
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Apply mobile-optimized settings
   */
  applyMobileOptimizations: () => {
    if (window.innerWidth < 768) {
      gsap.globalTimeline.timeScale(1.2); // Speed up animations on mobile
    }
  },
};

// Initialize GSAP when this module is imported
if (typeof window !== 'undefined') {
  initializeGSAP();
}
