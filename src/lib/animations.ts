/**
 * GSAP Animation Utilities for Quasari
 * Provides reusable animation patterns and configurations
 */

import gsap from 'gsap';

// Animation constants matching the design system
export const ANIMATION_CONFIG = {
  // Durations
  FAST: 0.2,
  NORMAL: 0.3,
  SLOW: 0.5,
  VERY_SLOW: 0.8,
  
  // Easings matching the design system
  EASE_OUT: 'power2.out',
  EASE_IN: 'power2.in',
  EASE_IN_OUT: 'power2.inOut',
  BOUNCE: 'back.out(1.7)',
  ELASTIC: 'elastic.out(1, 0.3)',
  
  // Colors from design system - Warm Orange Theme
  COLORS: {
    ACCENT: '#FF9566', // Main warm orange
    DARK: '#3D2914', // Dark warm brown
    CREAM: '#FBF7F2', // Warm cream
    WHITE: '#FEFCFA', // Warm white
    BACKGROUND: '#FEFCFA', // Main background
    SECONDARY: '#F5F0E8', // Secondary background
    PEACH: '#FFB088', // Light orange for accents
  },
  
  // Common transforms
  TRANSFORMS: {
    SCALE_UP: 1.05,
    SCALE_DOWN: 0.95,
    LIFT: -2,
    DROP: 2,
  }
} as const;

// Common animation presets
export const ANIMATION_PRESETS = {
  // Button hover effect
  buttonHover: {
    scale: ANIMATION_CONFIG.TRANSFORMS.SCALE_UP,
    y: ANIMATION_CONFIG.TRANSFORMS.LIFT,
    duration: ANIMATION_CONFIG.FAST,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
  
  // Button press effect
  buttonPress: {
    scale: ANIMATION_CONFIG.TRANSFORMS.SCALE_DOWN,
    duration: ANIMATION_CONFIG.FAST * 0.5,
    ease: ANIMATION_CONFIG.EASE_IN,
  },
  
  // Card hover effect
  cardHover: {
    y: ANIMATION_CONFIG.TRANSFORMS.LIFT,
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.08)',
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
  
  // Fade in animation
  fadeIn: {
    opacity: 1,
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
  
  // Slide in from bottom
  slideInUp: {
    y: 0,
    opacity: 1,
    duration: ANIMATION_CONFIG.SLOW,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
  
  // Slide in from right
  slideInRight: {
    x: 0,
    opacity: 1,
    duration: ANIMATION_CONFIG.SLOW,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
  
  // Scale in animation
  scaleIn: {
    scale: 1,
    opacity: 1,
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.BOUNCE,
  },
  
  // Pulse animation
  pulse: {
    scale: 1.1,
    duration: ANIMATION_CONFIG.SLOW,
    ease: ANIMATION_CONFIG.EASE_IN_OUT,
    yoyo: true,
    repeat: -1,
  },

  // Conversation transition animations
  conversationFadeOut: {
    opacity: 0,
    y: -20,
    duration: ANIMATION_CONFIG.FAST,
    ease: ANIMATION_CONFIG.EASE_IN,
  },

  conversationFadeIn: {
    opacity: 1,
    y: 0,
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },

  // Message bubble entrance
  messageBubbleUser: {
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.BOUNCE,
  },

  messageBubbleAssistant: {
    opacity: 1,
    x: 0,
    y: 0,
    scale: 1,
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.BOUNCE,
  },

  // Sidebar session animations
  sidebarSessionHover: {
    scale: 1.02,
    x: 4,
    duration: ANIMATION_CONFIG.FAST,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },

  sidebarSessionSelect: {
    backgroundColor: '#FFF8F3',
    borderColor: '#FF9566', // Updated to warm orange accent
    duration: ANIMATION_CONFIG.NORMAL,
    ease: ANIMATION_CONFIG.EASE_OUT,
  },
} as const;

// Animation helper functions
export const animationHelpers = {
  /**
   * Creates a stagger animation for multiple elements
   */
  stagger: (elements: string | Element[], animation: gsap.TweenVars, staggerAmount = 0.1) => {
    return gsap.to(elements, {
      ...animation,
      stagger: staggerAmount,
    });
  },
  
  /**
   * Creates a timeline for complex animations
   */
  createTimeline: (config?: gsap.TimelineVars) => {
    return gsap.timeline(config);
  },
  
  /**
   * Sets initial state for animations (useful for preventing FOUC)
   */
  setInitialState: (elements: string | Element[], state: gsap.TweenVars) => {
    return gsap.set(elements, state);
  },
  
  /**
   * Creates a hover animation with enter and leave states
   */
  createHoverAnimation: (
    element: string | Element,
    enterState: gsap.TweenVars,
    leaveState: gsap.TweenVars = {}
  ) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element;
    if (!el) return;
    
    const enter = () => gsap.to(el, enterState);
    const leave = () => gsap.to(el, { ...enterState, ...leaveState, duration: enterState.duration || ANIMATION_CONFIG.FAST });
    
    el.addEventListener('mouseenter', enter);
    el.addEventListener('mouseleave', leave);
    
    // Return cleanup function
    return () => {
      el.removeEventListener('mouseenter', enter);
      el.removeEventListener('mouseleave', leave);
    };
  },
  
  /**
   * Creates a loading spinner animation
   */
  createSpinner: (element: string | Element, config?: { speed?: number; direction?: number }) => {
    const { speed = 1, direction = 1 } = config || {};
    return gsap.to(element, {
      rotation: 360 * direction,
      duration: 1 / speed,
      ease: 'none',
      repeat: -1,
    });
  },
  
  /**
   * Creates a typing indicator animation (three dots)
   */
  createTypingIndicator: (elements: string | Element[]) => {
    return gsap.to(elements, {
      opacity: 0.3,
      duration: 0.6,
      ease: 'power2.inOut',
      stagger: 0.2,
      yoyo: true,
      repeat: -1,
    });
  },
  
  /**
   * Creates a progress bar animation
   */
  createProgressBar: (element: string | Element, progress: number, duration = ANIMATION_CONFIG.SLOW) => {
    return gsap.to(element, {
      width: `${progress}%`,
      duration,
      ease: ANIMATION_CONFIG.EASE_OUT,
    });
  },
  
  /**
   * Creates a notification slide-in animation
   */
  createNotificationSlide: (element: string | Element, direction: 'left' | 'right' | 'top' | 'bottom' = 'right') => {
    const initialState: gsap.TweenVars = { opacity: 0 };
    const finalState: gsap.TweenVars = { opacity: 1 };
    
    switch (direction) {
      case 'left':
        initialState.x = -100;
        finalState.x = 0;
        break;
      case 'right':
        initialState.x = 100;
        finalState.x = 0;
        break;
      case 'top':
        initialState.y = -100;
        finalState.y = 0;
        break;
      case 'bottom':
        initialState.y = 100;
        finalState.y = 0;
        break;
    }
    
    gsap.set(element, initialState);
    return gsap.to(element, {
      ...finalState,
      duration: ANIMATION_CONFIG.NORMAL,
      ease: ANIMATION_CONFIG.EASE_OUT,
    });
  },
};

// Reduced motion preferences
export const respectsReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Apply reduced motion settings
export const applyReducedMotionSettings = () => {
  if (respectsReducedMotion()) {
    gsap.globalTimeline.timeScale(0.1); // Slow down all animations
    // Or disable animations entirely:
    // gsap.globalTimeline.pause();
  }
};
