// Mild Orange Color Theme - Comprehensive Color Palette
// Research-based warm, subtle orange tones for modern web design

export const colors = {
  // Primary Orange Tones (Main Brand Colors)
  primary: {
    50: '#FFF8F3',   // Lightest cream with warm undertone
    100: '#FFEEE0',  // Very light peach cream
    200: '#FFE0C7',  // Soft peach
    300: '#FFCBA8',  // Light apricot
    400: '#FFB088',  // Medium peach
    500: '#FF9566',  // Main orange (warm, mild)
    600: '#E8804D',  // Deeper orange
    700: '#D16B34',  // Rich terracotta
    800: '#B8561F',  // Deep burnt orange
    900: '#9F4110',  // Darkest orange
  },

  // Secondary Warm Neutrals
  neutral: {
    50: '#FEFCFA',   // Pure warm white
    100: '#FBF7F2',  // Warm off-white
    200: '#F5F0E8',  // Light warm beige
    300: '#EDE4D8',  // Soft warm beige
    400: '#E0D2C1',  // Medium warm beige
    500: '#D1BFA8',  // Warm taupe
    600: '#BFA88C',  // Deeper taupe
    700: '#A68E6F',  // Warm brown
    800: '#8B7355',  // Deep warm brown
    900: '#6F5A3E',  // Darkest warm brown
  },

  // Accent Colors (Complementary)
  accent: {
    blue: '#7BA7BC',     // Soft blue (complement to orange)
    sage: '#A8B5A0',     // Sage green (natural complement)
    lavender: '#C4B5D6', // Soft lavender (triadic)
    coral: '#FF8A80',    // Coral (analogous)
  },

  // Semantic Colors (Status/Feedback)
  semantic: {
    success: '#8FBC8F',  // Soft sage green
    warning: '#FFB366',  // Warm orange
    error: '#E57373',    // Soft red
    info: '#81C4E8',     // Soft blue
  },

  // Text Colors
  text: {
    primary: '#3D2914',    // Dark warm brown
    secondary: '#6F5A3E',  // Medium warm brown
    muted: '#A68E6F',      // Light warm brown
    inverse: '#FEFCFA',    // Warm white for dark backgrounds
  },

  // Background Colors
  background: {
    primary: '#FEFCFA',    // Main background (warm white)
    secondary: '#FBF7F2',  // Secondary background
    tertiary: '#F5F0E8',   // Card/section backgrounds
    overlay: 'rgba(255, 149, 102, 0.1)', // Transparent orange overlay
  },

  // Border Colors
  border: {
    light: '#F5F0E8',      // Light borders
    medium: '#EDE4D8',     // Medium borders
    dark: '#D1BFA8',       // Dark borders
    focus: '#FF9566',      // Focus state borders
  },

  // Shadow Colors
  shadow: {
    sm: 'rgba(255, 149, 102, 0.1)',   // Small shadows
    md: 'rgba(255, 149, 102, 0.15)',  // Medium shadows
    lg: 'rgba(255, 149, 102, 0.2)',   // Large shadows
    xl: 'rgba(255, 149, 102, 0.25)',  // Extra large shadows
  },
} as const;

// CSS Custom Properties for easy integration
export const cssVariables = `
  :root {
    /* Primary Orange Tones */
    --color-primary-50: #FFF8F3;
    --color-primary-100: #FFEEE0;
    --color-primary-200: #FFE0C7;
    --color-primary-300: #FFCBA8;
    --color-primary-400: #FFB088;
    --color-primary-500: #FF9566;
    --color-primary-600: #E8804D;
    --color-primary-700: #D16B34;
    --color-primary-800: #B8561F;
    --color-primary-900: #9F4110;

    /* Neutral Warm Tones */
    --color-neutral-50: #FEFCFA;
    --color-neutral-100: #FBF7F2;
    --color-neutral-200: #F5F0E8;
    --color-neutral-300: #EDE4D8;
    --color-neutral-400: #E0D2C1;
    --color-neutral-500: #D1BFA8;
    --color-neutral-600: #BFA88C;
    --color-neutral-700: #A68E6F;
    --color-neutral-800: #8B7355;
    --color-neutral-900: #6F5A3E;

    /* Accent Colors */
    --color-accent-blue: #7BA7BC;
    --color-accent-sage: #A8B5A0;
    --color-accent-lavender: #C4B5D6;
    --color-accent-coral: #FF8A80;

    /* Text Colors */
    --color-text-primary: #3D2914;
    --color-text-secondary: #6F5A3E;
    --color-text-muted: #A68E6F;
    --color-text-inverse: #FEFCFA;

    /* Background Colors */
    --color-bg-primary: #FEFCFA;
    --color-bg-secondary: #FBF7F2;
    --color-bg-tertiary: #F5F0E8;

    /* Border Colors */
    --color-border-light: #F5F0E8;
    --color-border-medium: #EDE4D8;
    --color-border-dark: #D1BFA8;
    --color-border-focus: #FF9566;
  }
`;

// Tailwind CSS color configuration
export const tailwindColors = {
  primary: colors.primary,
  neutral: colors.neutral,
  accent: colors.accent,
  semantic: colors.semantic,
};
