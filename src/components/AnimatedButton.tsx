/**
 * Animated Button Component using GSAP
 * Provides enhanced button interactions with smooth animations
 */

import React, { useRef, ReactNode } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { ANIMATION_CONFIG, ANIMATION_PRESETS, respectsReducedMotion } from '../lib/animations';

gsap.registerPlugin(useGSAP);

interface AnimatedButtonProps {
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'ghost' | 'orange';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  style?: React.CSSProperties;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  disabled = false,
  variant = 'primary',
  size = 'md',
  className = '',
  type = 'button',
  loading = false,
  icon,
  iconPosition = 'left',
  style,
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const rippleRef = useRef<HTMLDivElement>(null);

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-black text-white hover:bg-gray-800';
      case 'secondary':
        return 'bg-white text-black border-2 border-gray-300 hover:border-gray-400';
      case 'ghost':
        return 'bg-transparent text-black hover:bg-gray-100';
      case 'orange':
        return 'text-black hover:bg-orange-300';
      default:
        return 'bg-black text-white hover:bg-gray-800';
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm';
      case 'md':
        return 'px-4 py-3 text-base';
      case 'lg':
        return 'px-6 py-4 text-lg';
      default:
        return 'px-4 py-3 text-base';
    }
  };

  // Setup hover animations
  useGSAP(() => {
    if (!buttonRef.current || respectsReducedMotion()) return;

    const button = buttonRef.current;
    
    const handleMouseEnter = () => {
      if (disabled || loading) return;
      
      gsap.to(button, {
        ...ANIMATION_PRESETS.buttonHover,
        backgroundColor: variant === 'orange' ? '#FFB088' : undefined,
      });
    };

    const handleMouseLeave = () => {
      if (disabled || loading) return;
      
      gsap.to(button, {
        scale: 1,
        y: 0,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
        backgroundColor: variant === 'orange' ? '#FF9566' : undefined,
      });
    };

    const handleMouseDown = () => {
      if (disabled || loading) return;
      
      gsap.to(button, ANIMATION_PRESETS.buttonPress);
    };

    const handleMouseUp = () => {
      if (disabled || loading) return;
      
      gsap.to(button, {
        scale: ANIMATION_CONFIG.TRANSFORMS.SCALE_UP,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    };

    button.addEventListener('mouseenter', handleMouseEnter);
    button.addEventListener('mouseleave', handleMouseLeave);
    button.addEventListener('mousedown', handleMouseDown);
    button.addEventListener('mouseup', handleMouseUp);

    return () => {
      button.removeEventListener('mouseenter', handleMouseEnter);
      button.removeEventListener('mouseleave', handleMouseLeave);
      button.removeEventListener('mousedown', handleMouseDown);
      button.removeEventListener('mouseup', handleMouseUp);
    };
  }, [disabled, loading, variant]);

  // Handle click with ripple effect
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Create ripple effect
    if (rippleRef.current && !respectsReducedMotion()) {
      const button = buttonRef.current!;
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      gsap.set(rippleRef.current, {
        width: size,
        height: size,
        left: x,
        top: y,
        scale: 0,
        opacity: 0.6,
      });

      gsap.to(rippleRef.current, {
        scale: 1,
        opacity: 0,
        duration: ANIMATION_CONFIG.SLOW,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }

    onClick?.();
  };

  // Loading animation
  useGSAP(() => {
    if (!contentRef.current) return;

    if (loading) {
      gsap.to(contentRef.current, {
        opacity: 0.6,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    } else {
      gsap.to(contentRef.current, {
        opacity: 1,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }
  }, [loading]);

  const baseStyles = `
    relative overflow-hidden font-medium transition-all duration-200 
    disabled:opacity-50 disabled:cursor-not-allowed
    focus:outline-none focus:ring-2 focus:ring-black/10
  `;

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const borderRadius = variant === 'orange' ? 'rounded-3xl' : 'rounded-3xl';
  const backgroundColor = variant === 'orange' ? '#FF9566' : '';

  return (
    <button
      ref={buttonRef}
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={`${baseStyles} ${variantStyles} ${sizeStyles} ${borderRadius} ${className}`}
      style={{
        backgroundColor: variant === 'orange' ? backgroundColor : undefined,
        letterSpacing: '-0.01em',
        ...style,
      }}
    >
      {/* Ripple effect */}
      <div
        ref={rippleRef}
        className="absolute rounded-full bg-white pointer-events-none"
        style={{ mixBlendMode: 'overlay' }}
      />
      
      {/* Button content */}
      <div
        ref={contentRef}
        className="relative flex items-center justify-center gap-2"
      >
        {icon && iconPosition === 'left' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        
        <span className="flex-1">{children}</span>
        
        {icon && iconPosition === 'right' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </div>
    </button>
  );
};

/**
 * Animated Icon Button - for icon-only buttons
 */
interface AnimatedIconButtonProps {
  icon: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'ghost' | 'filled';
  className?: string;
  ariaLabel?: string;
}

export const AnimatedIconButton: React.FC<AnimatedIconButtonProps> = ({
  icon,
  onClick,
  disabled = false,
  size = 'md',
  variant = 'ghost',
  className = '',
  ariaLabel,
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'p-2';
      case 'md':
        return 'p-3';
      case 'lg':
        return 'p-4';
      default:
        return 'p-3';
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'ghost':
        return 'hover:bg-gray-100';
      case 'filled':
        return 'bg-gray-100 hover:bg-gray-200';
      default:
        return 'hover:bg-gray-100';
    }
  };

  // Setup hover animations
  useGSAP(() => {
    if (!buttonRef.current || respectsReducedMotion()) return;

    const button = buttonRef.current;
    
    const handleMouseEnter = () => {
      if (disabled) return;
      
      gsap.to(button, {
        scale: ANIMATION_CONFIG.TRANSFORMS.SCALE_UP,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    };

    const handleMouseLeave = () => {
      if (disabled) return;
      
      gsap.to(button, {
        scale: 1,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    };

    button.addEventListener('mouseenter', handleMouseEnter);
    button.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      button.removeEventListener('mouseenter', handleMouseEnter);
      button.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [disabled]);

  const sizeStyles = getSizeStyles();
  const variantStyles = getVariantStyles();

  return (
    <button
      ref={buttonRef}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      className={`
        relative rounded-xl transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        focus:outline-none focus:ring-2 focus:ring-black/10
        ${sizeStyles} ${variantStyles} ${className}
      `}
    >
      {icon}
    </button>
  );
};
