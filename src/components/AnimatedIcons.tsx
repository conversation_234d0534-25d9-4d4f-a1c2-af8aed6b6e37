/**
 * Animated Icons Components using GSAP
 * Provides reusable animated icons for the Quasari application
 */

import React, { useRef, useEffect } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { ANIMATION_CONFIG, animationHelpers } from '../lib/animations';

// Register the hook
gsap.registerPlugin(useGSAP);

interface AnimatedIconProps {
  className?: string;
  size?: number;
  color?: string;
  speed?: number;
}

/**
 * Animated Loading Spinner - Replaces the basic Tailwind spinner
 */
export const AnimatedSpinner: React.FC<AnimatedIconProps> = ({ 
  className = '', 
  size = 24, 
  color = ANIMATION_CONFIG.COLORS.DARK,
  speed = 1 
}) => {
  const spinnerRef = useRef<SVGSVGElement>(null);

  useGSAP(() => {
    if (spinnerRef.current) {
      animationHelpers.createSpinner(spinnerRef.current, { speed });
    }
  }, [speed]);

  return (
    <svg
      ref={spinnerRef}
      className={`inline-block ${className}`}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeDasharray="31.416"
        strokeDashoffset="31.416"
        fill="none"
        opacity="0.3"
      />
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeDasharray="31.416"
        strokeDashoffset="23.562"
        fill="none"
      />
    </svg>
  );
};

/**
 * Animated Hamburger Menu Icon
 */
export const AnimatedHamburger: React.FC<AnimatedIconProps & { isOpen: boolean; onClick?: () => void }> = ({ 
  className = '', 
  size = 24, 
  color = ANIMATION_CONFIG.COLORS.DARK,
  isOpen,
  onClick 
}) => {
  const containerRef = useRef<SVGSVGElement>(null);
  const line1Ref = useRef<SVGPathElement>(null);
  const line2Ref = useRef<SVGPathElement>(null);
  const line3Ref = useRef<SVGPathElement>(null);

  useGSAP(() => {
    if (!line1Ref.current || !line2Ref.current || !line3Ref.current) return;

    const tl = gsap.timeline();

    if (isOpen) {
      tl.to(line1Ref.current, {
        rotation: 45,
        y: 6,
        duration: ANIMATION_CONFIG.NORMAL,
        ease: ANIMATION_CONFIG.EASE_OUT,
      })
      .to(line2Ref.current, {
        opacity: 0,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      }, 0)
      .to(line3Ref.current, {
        rotation: -45,
        y: -6,
        duration: ANIMATION_CONFIG.NORMAL,
        ease: ANIMATION_CONFIG.EASE_OUT,
      }, 0);
    } else {
      tl.to([line1Ref.current, line3Ref.current], {
        rotation: 0,
        y: 0,
        duration: ANIMATION_CONFIG.NORMAL,
        ease: ANIMATION_CONFIG.EASE_OUT,
      })
      .to(line2Ref.current, {
        opacity: 1,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      }, 0);
    }
  }, [isOpen]);

  return (
    <svg
      ref={containerRef}
      className={`cursor-pointer ${className}`}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <path
        ref={line1Ref}
        d="M4 6h16"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        style={{ transformOrigin: 'center' }}
      />
      <path
        ref={line2Ref}
        d="M4 12h16"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />
      <path
        ref={line3Ref}
        d="M4 18h16"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        style={{ transformOrigin: 'center' }}
      />
    </svg>
  );
};

/**
 * Animated Search Icon with pulse effect
 */
export const AnimatedSearchIcon: React.FC<AnimatedIconProps & { isActive?: boolean }> = ({ 
  className = '', 
  size = 24, 
  color = ANIMATION_CONFIG.COLORS.DARK,
  isActive = false 
}) => {
  const iconRef = useRef<SVGSVGElement>(null);

  useGSAP(() => {
    if (!iconRef.current) return;

    if (isActive) {
      gsap.to(iconRef.current, {
        scale: 1.1,
        duration: ANIMATION_CONFIG.SLOW,
        ease: ANIMATION_CONFIG.EASE_IN_OUT,
        yoyo: true,
        repeat: -1,
      });
    } else {
      gsap.to(iconRef.current, {
        scale: 1,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }
  }, [isActive]);

  return (
    <svg
      ref={iconRef}
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="11"
        cy="11"
        r="8"
        stroke={color}
        strokeWidth="2"
      />
      <path
        d="m21 21-4.35-4.35"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );
};

/**
 * Animated Check Icon with draw-on effect
 */
export const AnimatedCheckIcon: React.FC<AnimatedIconProps & { isVisible?: boolean }> = ({ 
  className = '', 
  size = 24, 
  color = '#10B981',
  isVisible = false 
}) => {
  const pathRef = useRef<SVGPathElement>(null);

  useGSAP(() => {
    if (!pathRef.current) return;

    if (isVisible) {
      gsap.fromTo(pathRef.current, 
        {
          strokeDasharray: 20,
          strokeDashoffset: 20,
        },
        {
          strokeDashoffset: 0,
          duration: ANIMATION_CONFIG.SLOW,
          ease: ANIMATION_CONFIG.EASE_OUT,
        }
      );
    } else {
      gsap.set(pathRef.current, {
        strokeDashoffset: 20,
      });
    }
  }, [isVisible]);

  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        ref={pathRef}
        d="M5 13l4 4L19 7"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

/**
 * Animated Plus Icon that rotates to X
 */
export const AnimatedPlusIcon: React.FC<AnimatedIconProps & { isOpen: boolean; onClick?: () => void }> = ({ 
  className = '', 
  size = 24, 
  color = ANIMATION_CONFIG.COLORS.DARK,
  isOpen,
  onClick 
}) => {
  const iconRef = useRef<SVGSVGElement>(null);

  useGSAP(() => {
    if (!iconRef.current) return;

    gsap.to(iconRef.current, {
      rotation: isOpen ? 45 : 0,
      duration: ANIMATION_CONFIG.NORMAL,
      ease: ANIMATION_CONFIG.EASE_OUT,
    });
  }, [isOpen]);

  return (
    <svg
      ref={iconRef}
      className={`cursor-pointer ${className}`}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      style={{ transformOrigin: 'center' }}
    >
      <path
        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );
};

/**
 * Animated Typing Indicator (three dots)
 */
export const AnimatedTypingIndicator: React.FC<{ className?: string; size?: number; color?: string }> = ({ 
  className = '', 
  size = 8, 
  color = ANIMATION_CONFIG.COLORS.DARK 
}) => {
  const dot1Ref = useRef<HTMLDivElement>(null);
  const dot2Ref = useRef<HTMLDivElement>(null);
  const dot3Ref = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    const dots = [dot1Ref.current, dot2Ref.current, dot3Ref.current].filter(Boolean);
    if (dots.length > 0) {
      animationHelpers.createTypingIndicator(dots);
    }
  });

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div
        ref={dot1Ref}
        className="rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color,
        }}
      />
      <div
        ref={dot2Ref}
        className="rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color,
        }}
      />
      <div
        ref={dot3Ref}
        className="rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color,
        }}
      />
    </div>
  );
};
