/**
 * Animated Progress Bar Component using GSAP
 * Provides smooth progress animations for the search interface
 */

import React, { useRef, useEffect } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { ANIMATION_CONFIG, animationHelpers, respectsReducedMotion } from '../lib/animations';

gsap.registerPlugin(useGSAP);

interface AnimatedProgressBarProps {
  progress: number; // 0-100
  className?: string;
  height?: number;
  color?: string;
  backgroundColor?: string;
  showPercentage?: boolean;
  animated?: boolean;
  duration?: number;
  label?: string;
}

export const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({
  progress,
  className = '',
  height = 24,
  color = ANIMATION_CONFIG.COLORS.ACCENT,
  backgroundColor = '#E5E7EB',
  showPercentage = true,
  animated = true,
  duration = ANIMATION_CONFIG.SLOW,
  label,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const fillRef = useRef<HTMLDivElement>(null);
  const percentageRef = useRef<HTMLSpanElement>(null);

  // Animate progress changes
  useGSAP(() => {
    if (!fillRef.current || respectsReducedMotion()) {
      // If reduced motion is preferred, set immediately
      if (fillRef.current) {
        gsap.set(fillRef.current, { width: `${progress}%` });
      }
      if (percentageRef.current) {
        gsap.set(percentageRef.current, { textContent: `${Math.round(progress)}%` });
      }
      return;
    }

    if (animated) {
      // Animate the progress bar fill
      gsap.to(fillRef.current, {
        width: `${progress}%`,
        duration,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });

      // Animate the percentage counter
      if (percentageRef.current && showPercentage) {
        gsap.to({ value: 0 }, {
          value: progress,
          duration,
          ease: ANIMATION_CONFIG.EASE_OUT,
          onUpdate: function() {
            if (percentageRef.current) {
              percentageRef.current.textContent = `${Math.round(this.targets()[0].value)}%`;
            }
          }
        });
      }
    } else {
      // Set immediately without animation
      gsap.set(fillRef.current, { width: `${progress}%` });
      if (percentageRef.current) {
        percentageRef.current.textContent = `${Math.round(progress)}%`;
      }
    }
  }, [progress, animated, duration, showPercentage]);

  return (
    <div className={`w-full ${className}`}>
      {/* Label */}
      {label && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          {showPercentage && (
            <span
              ref={percentageRef}
              className="text-sm font-bold text-gray-900"
            >
              0%
            </span>
          )}
        </div>
      )}
      
      {/* Progress Bar */}
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{
          height: `${height}px`,
          backgroundColor,
          borderRadius: `${height / 2}px`,
        }}
      >
        {/* Progress Fill */}
        <div
          ref={fillRef}
          className="absolute top-0 left-0 h-full transition-all"
          style={{
            width: '0%',
            backgroundColor: color,
            borderRadius: `${height / 2}px`,
          }}
        />
        
        {/* Percentage Text (centered) */}
        {showPercentage && !label && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span
              ref={percentageRef}
              className="text-xs font-bold text-gray-900 mix-blend-difference"
            >
              0%
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Animated Step Progress Bar - for multi-step processes
 */
interface Step {
  label: string;
  completed: boolean;
  active?: boolean;
}

interface AnimatedStepProgressProps {
  steps: Step[];
  className?: string;
  activeColor?: string;
  completedColor?: string;
  inactiveColor?: string;
}

export const AnimatedStepProgress: React.FC<AnimatedStepProgressProps> = ({
  steps,
  className = '',
  activeColor = ANIMATION_CONFIG.COLORS.ACCENT,
  completedColor = '#10B981',
  inactiveColor = '#D1D5DB',
}) => {
  const stepsRef = useRef<(HTMLDivElement | null)[]>([]);
  const connectorsRef = useRef<(HTMLDivElement | null)[]>([]);

  // Animate step changes
  useGSAP(() => {
    if (respectsReducedMotion()) return;

    steps.forEach((step, index) => {
      const stepElement = stepsRef.current[index];
      const connectorElement = connectorsRef.current[index];

      if (stepElement) {
        const targetColor = step.completed ? completedColor : step.active ? activeColor : inactiveColor;
        const targetScale = step.active ? 1.2 : 1;

        gsap.to(stepElement, {
          backgroundColor: targetColor,
          scale: targetScale,
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.EASE_OUT,
        });
      }

      if (connectorElement && index < steps.length - 1) {
        const nextStep = steps[index + 1];
        const targetColor = step.completed && nextStep.completed ? completedColor : inactiveColor;

        gsap.to(connectorElement, {
          backgroundColor: targetColor,
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.EASE_OUT,
        });
      }
    });
  }, [steps, activeColor, completedColor, inactiveColor]);

  return (
    <div className={`flex items-center ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          {/* Step Circle */}
          <div className="flex flex-col items-center">
            <div
              ref={(el) => (stepsRef.current[index] = el)}
              className="w-4 h-4 rounded-full flex items-center justify-center"
              style={{
                backgroundColor: step.completed ? completedColor : step.active ? activeColor : inactiveColor,
              }}
            >
              {step.completed && (
                <svg
                  className="w-2 h-2 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span className="text-xs mt-2 text-center font-medium text-gray-600">
              {step.label}
            </span>
          </div>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div
              ref={(el) => (connectorsRef.current[index] = el)}
              className="flex-1 h-0.5 mx-2"
              style={{
                backgroundColor: step.completed && steps[index + 1].completed ? completedColor : inactiveColor,
              }}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

/**
 * Animated Circular Progress - for loading states
 */
interface AnimatedCircularProgressProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showPercentage?: boolean;
  className?: string;
}

export const AnimatedCircularProgress: React.FC<AnimatedCircularProgressProps> = ({
  progress,
  size = 120,
  strokeWidth = 8,
  color = ANIMATION_CONFIG.COLORS.ACCENT,
  backgroundColor = '#E5E7EB',
  showPercentage = true,
  className = '',
}) => {
  const circleRef = useRef<SVGCircleElement>(null);
  const textRef = useRef<SVGTextElement>(null);

  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  useGSAP(() => {
    if (!circleRef.current || respectsReducedMotion()) {
      if (circleRef.current) {
        const offset = circumference - (progress / 100) * circumference;
        gsap.set(circleRef.current, { strokeDashoffset: offset });
      }
      if (textRef.current) {
        gsap.set(textRef.current, { textContent: `${Math.round(progress)}%` });
      }
      return;
    }

    // Animate the circular progress
    const targetOffset = circumference - (progress / 100) * circumference;
    
    gsap.to(circleRef.current, {
      strokeDashoffset: targetOffset,
      duration: ANIMATION_CONFIG.SLOW,
      ease: ANIMATION_CONFIG.EASE_OUT,
    });

    // Animate the percentage text
    if (textRef.current && showPercentage) {
      gsap.to({ value: 0 }, {
        value: progress,
        duration: ANIMATION_CONFIG.SLOW,
        ease: ANIMATION_CONFIG.EASE_OUT,
        onUpdate: function() {
          if (textRef.current) {
            textRef.current.textContent = `${Math.round(this.targets()[0].value)}%`;
          }
        }
      });
    }
  }, [progress, circumference, showPercentage]);

  return (
    <div className={`relative ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background Circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        
        {/* Progress Circle */}
        <circle
          ref={circleRef}
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={circumference}
        />
      </svg>
      
      {/* Percentage Text */}
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-lg font-bold text-gray-900">
            <svg width={size} height={size} className="absolute">
              <text
                ref={textRef}
                x="50%"
                y="50%"
                textAnchor="middle"
                dy="0.3em"
                className="text-lg font-bold fill-current"
              >
                0%
              </text>
            </svg>
          </span>
        </div>
      )}
    </div>
  );
};
