/**
 * Page Transition Component using GSAP
 * Provides smooth page and component transitions
 */

import React, { useRef, ReactNode } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { ANIMATION_CONFIG, ANIMATION_PRESETS, respectsReducedMotion } from '../lib/animations';

gsap.registerPlugin(useGSAP);

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade' | 'scale';
  duration?: number;
  delay?: number;
  trigger?: boolean; // When true, triggers the animation
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  direction = 'fade',
  duration = ANIMATION_CONFIG.SLOW,
  delay = 0,
  trigger = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Set initial state and animate in
  useGSAP(() => {
    if (!containerRef.current || respectsReducedMotion()) {
      // If reduced motion is preferred, just show immediately
      if (containerRef.current) {
        gsap.set(containerRef.current, { opacity: 1, x: 0, y: 0, scale: 1 });
      }
      return;
    }

    if (trigger) {
      // Set initial state based on direction
      const initialState: gsap.TweenVars = { opacity: 0 };
      const finalState: gsap.TweenVars = { opacity: 1 };

      switch (direction) {
        case 'up':
          initialState.y = 50;
          finalState.y = 0;
          break;
        case 'down':
          initialState.y = -50;
          finalState.y = 0;
          break;
        case 'left':
          initialState.x = 50;
          finalState.x = 0;
          break;
        case 'right':
          initialState.x = -50;
          finalState.x = 0;
          break;
        case 'scale':
          initialState.scale = 0.8;
          finalState.scale = 1;
          break;
        case 'fade':
        default:
          // Already set opacity
          break;
      }

      // Set initial state
      gsap.set(containerRef.current, initialState);

      // Animate to final state
      gsap.to(containerRef.current, {
        ...finalState,
        duration,
        delay,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }
  }, [direction, duration, delay, trigger]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
};

/**
 * Stagger Animation Component - animates children with stagger effect
 */
interface StaggerAnimationProps {
  children: ReactNode[];
  className?: string;
  staggerAmount?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade' | 'scale';
  duration?: number;
  delay?: number;
}

export const StaggerAnimation: React.FC<StaggerAnimationProps> = ({
  children,
  className = '',
  staggerAmount = 0.1,
  direction = 'up',
  duration = ANIMATION_CONFIG.NORMAL,
  delay = 0,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (!containerRef.current || respectsReducedMotion()) {
      return;
    }

    const childElements = containerRef.current.children;
    if (childElements.length === 0) return;

    // Set initial state based on direction
    const initialState: gsap.TweenVars = { opacity: 0 };
    const finalState: gsap.TweenVars = { opacity: 1 };

    switch (direction) {
      case 'up':
        initialState.y = 30;
        finalState.y = 0;
        break;
      case 'down':
        initialState.y = -30;
        finalState.y = 0;
        break;
      case 'left':
        initialState.x = 30;
        finalState.x = 0;
        break;
      case 'right':
        initialState.x = -30;
        finalState.x = 0;
        break;
      case 'scale':
        initialState.scale = 0.8;
        finalState.scale = 1;
        break;
      case 'fade':
      default:
        // Already set opacity
        break;
    }

    // Set initial state for all children
    gsap.set(childElements, initialState);

    // Animate with stagger
    gsap.to(childElements, {
      ...finalState,
      duration,
      delay,
      stagger: staggerAmount,
      ease: ANIMATION_CONFIG.EASE_OUT,
    });
  }, [staggerAmount, direction, duration, delay]);

  return (
    <div ref={containerRef} className={className}>
      {children.map((child, index) => (
        <div key={index}>{child}</div>
      ))}
    </div>
  );
};

/**
 * Reveal on Scroll Component - animates when element comes into view
 */
interface RevealOnScrollProps {
  children: ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade' | 'scale';
  duration?: number;
  threshold?: number; // Intersection threshold (0-1)
  once?: boolean; // Whether to animate only once
}

export const RevealOnScroll: React.FC<RevealOnScrollProps> = ({
  children,
  className = '',
  direction = 'up',
  duration = ANIMATION_CONFIG.SLOW,
  threshold = 0.1,
  once = true,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const hasAnimated = useRef(false);

  useGSAP(() => {
    if (!elementRef.current || respectsReducedMotion()) {
      return;
    }

    // Set initial state based on direction
    const initialState: gsap.TweenVars = { opacity: 0 };
    const finalState: gsap.TweenVars = { opacity: 1 };

    switch (direction) {
      case 'up':
        initialState.y = 50;
        finalState.y = 0;
        break;
      case 'down':
        initialState.y = -50;
        finalState.y = 0;
        break;
      case 'left':
        initialState.x = 50;
        finalState.x = 0;
        break;
      case 'right':
        initialState.x = -50;
        finalState.x = 0;
        break;
      case 'scale':
        initialState.scale = 0.8;
        finalState.scale = 1;
        break;
      case 'fade':
      default:
        // Already set opacity
        break;
    }

    // Set initial state
    gsap.set(elementRef.current, initialState);

    // Create intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && (!once || !hasAnimated.current)) {
            gsap.to(entry.target, {
              ...finalState,
              duration,
              ease: ANIMATION_CONFIG.EASE_OUT,
            });
            hasAnimated.current = true;
            
            if (once) {
              observer.unobserve(entry.target);
            }
          } else if (!entry.isIntersecting && !once) {
            // Reset if not once and out of view
            gsap.to(entry.target, {
              ...initialState,
              duration: duration * 0.5,
              ease: ANIMATION_CONFIG.EASE_IN,
            });
          }
        });
      },
      { threshold }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [direction, duration, threshold, once]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
};

/**
 * Hover Scale Component - scales element on hover
 */
interface HoverScaleProps {
  children: ReactNode;
  className?: string;
  scale?: number;
  duration?: number;
}

export const HoverScale: React.FC<HoverScaleProps> = ({
  children,
  className = '',
  scale = ANIMATION_CONFIG.TRANSFORMS.SCALE_UP,
  duration = ANIMATION_CONFIG.FAST,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (!elementRef.current || respectsReducedMotion()) {
      return;
    }

    const element = elementRef.current;

    const handleMouseEnter = () => {
      gsap.to(element, {
        scale,
        duration,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        duration,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [scale, duration]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
};
