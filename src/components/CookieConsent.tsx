import { useState, useEffect } from 'react';
import { AnimatedButton } from './AnimatedButton';

interface CookieConsentProps {
  onAccept: () => void;
  onSettings?: () => void;
}

export function CookieConsent({ onAccept, onSettings }: CookieConsentProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already accepted cookies
    const hasAccepted = localStorage.getItem('cookies-accepted');
    if (!hasAccepted) {
      setIsVisible(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookies-accepted', 'true');
    setIsVisible(false);
    onAccept();
  };

  const handleSettings = () => {
    if (onSettings) {
      onSettings();
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/20 backdrop-blur-sm">
      <div 
        className="max-w-md w-full p-8 shadow-2xl"
        style={{
          background: '#C8A2C8', // Lavender background from reference
          borderRadius: '32px',
        }}
      >
        {/* Main consent text */}
        <div className="mb-8">
          <p
            className="text-black text-lg leading-relaxed"
            style={{
              lineHeight: '1.5',
              fontFamily: 'Forum, serif',
              fontWeight: '400'
            }}
          >
            By using this website, you agree that browsing cookies are stored on your device in order to improve site navigation, analyze your use of it and contribute to our marketing efforts.
          </p>
        </div>

        {/* Accept button */}
        <div className="flex justify-center mb-6">
          <button
            onClick={handleAccept}
            className="px-8 py-4 text-black text-lg transition-all duration-200 hover:transform hover:scale-105 hover:shadow-lg"
            style={{
              background: '#E8FF59', // Yellow-green from reference
              borderRadius: '32px',
              border: 'none',
              fontFamily: 'Forum, serif',
              fontWeight: '400',
              letterSpacing: '-0.01em'
            }}
          >
            I understand
          </button>
        </div>

        {/* Settings link */}
        <div className="text-center">
          <button
            onClick={handleSettings}
            className="text-black text-base underline hover:no-underline transition-all duration-200"
            style={{
              textDecorationThickness: '2px',
              textUnderlineOffset: '4px',
              fontFamily: 'Forum, serif',
              fontWeight: '400'
            }}
          >
            You can change your preferences in the settings
          </button>
        </div>
      </div>
    </div>
  );
}

// Hook to manage cookie consent state
export function useCookieConsent() {
  const [hasAccepted, setHasAccepted] = useState(false);

  useEffect(() => {
    const accepted = localStorage.getItem('cookies-accepted');
    setHasAccepted(!!accepted);
  }, []);

  const acceptCookies = () => {
    localStorage.setItem('cookies-accepted', 'true');
    setHasAccepted(true);
  };

  const resetConsent = () => {
    localStorage.removeItem('cookies-accepted');
    setHasAccepted(false);
  };

  return {
    hasAccepted,
    acceptCookies,
    resetConsent,
  };
}
