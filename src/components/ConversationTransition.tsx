/**
 * Conversation Transition Components using GSAP
 * Provides smooth animations for conversation switching and message transitions
 */

import React, { useRef, useEffect, ReactNode } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import { ANIMATION_CONFIG, respectsReducedMotion } from '../lib/animations';

gsap.registerPlugin(useGSAP);

interface ConversationTransitionProps {
  children: ReactNode;
  sessionId: string | null;
  isLoading?: boolean;
  className?: string;
}

/**
 * Main conversation container with transition animations
 */
export const ConversationTransition: React.FC<ConversationTransitionProps> = ({
  children,
  sessionId,
  isLoading = false,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const previousSessionId = useRef<string | null>(null);

  useGSAP(() => {
    if (!containerRef.current || respectsReducedMotion()) {
      return;
    }

    // Check if session has changed
    const hasSessionChanged = previousSessionId.current !== null && 
                             previousSessionId.current !== sessionId;

    if (hasSessionChanged) {
      // Animate out old content, then animate in new content
      const tl = gsap.timeline();
      
      // Fade out current content
      tl.to(containerRef.current, {
        opacity: 0,
        y: -20,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_IN,
      })
      // Set new content and animate in
      .set(containerRef.current, {
        y: 20,
      })
      .to(containerRef.current, {
        opacity: 1,
        y: 0,
        duration: ANIMATION_CONFIG.NORMAL,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    } else if (sessionId && !previousSessionId.current) {
      // Initial load animation
      gsap.fromTo(containerRef.current,
        { opacity: 0, y: 20 },
        { 
          opacity: 1, 
          y: 0, 
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.EASE_OUT 
        }
      );
    }

    // Update previous session ID
    previousSessionId.current = sessionId;
  }, [sessionId]);

  // Loading state animation
  useGSAP(() => {
    if (!containerRef.current || respectsReducedMotion()) {
      return;
    }

    if (isLoading) {
      gsap.to(containerRef.current, {
        opacity: 0.6,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    } else {
      gsap.to(containerRef.current, {
        opacity: 1,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }
  }, [isLoading]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
};

interface MessageListTransitionProps {
  children: ReactNode[];
  sessionId: string | null;
  className?: string;
}

/**
 * Message list with staggered entrance animations
 */
export const MessageListTransition: React.FC<MessageListTransitionProps> = ({
  children,
  sessionId,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const previousSessionId = useRef<string | null>(null);

  useGSAP(() => {
    if (!containerRef.current || respectsReducedMotion()) {
      return;
    }

    const hasSessionChanged = previousSessionId.current !== null && 
                             previousSessionId.current !== sessionId;

    if (hasSessionChanged || (sessionId && !previousSessionId.current)) {
      // Get all message elements
      const messageElements = containerRef.current.children;
      
      if (messageElements.length > 0) {
        // Set initial state for all messages
        gsap.set(messageElements, {
          opacity: 0,
          y: 30,
          scale: 0.95,
        });

        // Animate messages in with stagger
        gsap.to(messageElements, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.EASE_OUT,
          stagger: {
            amount: Math.min(messageElements.length * 0.05, 0.8), // Max 0.8s total stagger
            from: "start",
          },
        });
      }
    }

    previousSessionId.current = sessionId;
  }, [sessionId, children.length]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
};

interface MessageBubbleTransitionProps {
  children: ReactNode;
  messageId: string;
  messageType: 'user' | 'assistant' | 'progress';
  isNew?: boolean;
  className?: string;
}

/**
 * Individual message bubble with entrance animation
 */
export const MessageBubbleTransition: React.FC<MessageBubbleTransitionProps> = ({
  children,
  messageId,
  messageType,
  isNew = false,
  className = '',
}) => {
  const bubbleRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (!bubbleRef.current || respectsReducedMotion()) {
      return;
    }

    if (isNew) {
      // Animate new messages
      const direction = messageType === 'user' ? 'right' : 'left';
      const initialX = direction === 'right' ? 50 : -50;

      gsap.fromTo(bubbleRef.current,
        {
          opacity: 0,
          x: initialX,
          y: 20,
          scale: 0.9,
        },
        {
          opacity: 1,
          x: 0,
          y: 0,
          scale: 1,
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.BOUNCE,
        }
      );
    }
  }, [isNew, messageType]);

  return (
    <div ref={bubbleRef} className={className}>
      {children}
    </div>
  );
};

interface SidebarSessionTransitionProps {
  children: ReactNode;
  sessionId: string;
  isSelected?: boolean;
  isHovered?: boolean;
  className?: string;
}

/**
 * Sidebar session item with hover and selection animations
 */
export const SidebarSessionTransition: React.FC<SidebarSessionTransitionProps> = ({
  children,
  sessionId,
  isSelected = false,
  isHovered = false,
  className = '',
}) => {
  const sessionRef = useRef<HTMLDivElement>(null);

  // Hover animation
  useGSAP(() => {
    if (!sessionRef.current || respectsReducedMotion()) {
      return;
    }

    if (isHovered) {
      gsap.to(sessionRef.current, {
        scale: 1.02,
        x: 4,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    } else {
      gsap.to(sessionRef.current, {
        scale: 1,
        x: 0,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_OUT,
      });
    }
  }, [isHovered]);

  // Selection animation - removed background/border overrides to preserve button styling

  return (
    <div ref={sessionRef} className={className}>
      {children}
    </div>
  );
};

interface LoadingTransitionProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

/**
 * Loading state with smooth transitions
 */
export const LoadingTransition: React.FC<LoadingTransitionProps> = ({
  isVisible,
  message = 'Loading conversation...',
  className = '',
}) => {
  const loadingRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (!loadingRef.current || respectsReducedMotion()) {
      if (loadingRef.current) {
        loadingRef.current.style.display = isVisible ? 'flex' : 'none';
      }
      return;
    }

    if (isVisible) {
      gsap.set(loadingRef.current, { display: 'flex' });
      gsap.fromTo(loadingRef.current,
        { opacity: 0, y: 20 },
        { 
          opacity: 1, 
          y: 0, 
          duration: ANIMATION_CONFIG.NORMAL,
          ease: ANIMATION_CONFIG.EASE_OUT 
        }
      );
    } else {
      gsap.to(loadingRef.current, {
        opacity: 0,
        y: -20,
        duration: ANIMATION_CONFIG.FAST,
        ease: ANIMATION_CONFIG.EASE_IN,
        onComplete: () => {
          if (loadingRef.current) {
            loadingRef.current.style.display = 'none';
          }
        }
      });
    }
  }, [isVisible]);

  return (
    <div
      ref={loadingRef}
      className={`fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50 ${className}`}
      style={{ display: 'none' }}
    >
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
        <p className="text-gray-600 font-medium">{message}</p>
      </div>
    </div>
  );
};
