"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();

  if (!isAuthenticated) {
    return null;
  }

  return (
    <button
      className="px-4 py-2 text-white font-medium hover:bg-gray-800 transition-all duration-150 text-sm"
      style={{
        backgroundColor: '#000000',
        borderRadius: '20px',
        border: 'none',
        letterSpacing: '-0.01em'
      }}
      onClick={() => void signOut()}
    >
      Sign out
    </button>
  );
}
