import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useAction, useConvexAuth } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';

export function GmailSettings() {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  // Get authentication state
  const { isAuthenticated, isLoading } = useConvexAuth();
  const loggedInUser = useQuery(api.auth.loggedInUser);

  // Get Gmail authentication status
  const gmailStatus = useQuery(api.gmailActions.getGmailAuthStatus);

  // Actions and Mutations
  const getAuthUrl = useAction(api.gmailActions.getGmailAuthUrl);
  const disconnectGmail = useAction(api.gmailActions.disconnectGmail);

  // Comprehensive logging of authentication state
  useEffect(() => {
    console.log('[GMAIL SETTINGS] Authentication state changed:', {
      isAuthenticated,
      isLoading,
      loggedInUser: loggedInUser ? {
        id: loggedInUser._id,
        email: loggedInUser.email || 'no-email',
        keys: Object.keys(loggedInUser)
      } : null,
      gmailStatus,
      timestamp: new Date().toISOString()
    });

    // OAuth callback is now handled in the main App component
  }, [isAuthenticated, isLoading, loggedInUser, gmailStatus]);

  // OAuth callback is now handled in the main App component

  const handleConnectGmail = async () => {
    console.log('[GMAIL CONNECT] Starting Gmail connection...', {
      isAuthenticated,
      isLoading,
      loggedInUser: loggedInUser ? { id: loggedInUser._id, email: loggedInUser.email } : null,
      timestamp: new Date().toISOString()
    });

    // Check if user is anonymous
    if (!loggedInUser?.email || loggedInUser.email === 'anonymous') {
      console.log('[GMAIL CONNECT] Anonymous user detected, blocking Gmail connection');
      toast.error('Gmail connection requires a registered account', {
        duration: 8000,
        description: 'Please sign up with an email address to connect your Gmail account.'
      });
      return;
    }

    setIsConnecting(true);
    try {
      console.log('[GMAIL CONNECT] Getting auth URL...');
      const result = await getAuthUrl();
      console.log('[GMAIL CONNECT] Auth URL result:', result);

      if (result.authUrl) {
        console.log('[GMAIL CONNECT] Storing OAuth state and redirecting...', {
          authUrl: result.authUrl,
          currentUrl: window.location.href,
          isAuthenticated,
          loggedInUser: loggedInUser ? { id: loggedInUser._id } : null
        });

        // Store the current state to restore after OAuth
        localStorage.setItem('gmail_oauth_in_progress', 'true');
        localStorage.setItem('gmail_oauth_return_path', '/settings');
        localStorage.setItem('gmail_oauth_user_id', loggedInUser?._id || '');
        localStorage.setItem('gmail_oauth_user_email', loggedInUser?.email || '');
        localStorage.setItem('gmail_oauth_timestamp', Date.now().toString());

        console.log('[GMAIL CONNECT] Redirecting to Google OAuth...');
        // Redirect to Google OAuth in the same window
        window.location.href = result.authUrl;
      } else {
        throw new Error('No auth URL received from server');
      }
    } catch (error) {
      console.error('[GMAIL CONNECT] Gmail connection error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to connect Gmail account');
      setIsConnecting(false);
    }
  };

  const handleDisconnectGmail = async () => {
    setIsDisconnecting(true);
    try {
      await disconnectGmail();
      toast.success('Gmail account disconnected successfully');
    } catch (error) {
      console.error('Gmail disconnection error:', error);
      toast.error('Failed to disconnect Gmail account');
    } finally {
      setIsDisconnecting(false);
    }
  };

  if (!gmailStatus) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const isAnonymousUser = !loggedInUser?.email || loggedInUser.email === 'anonymous';

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Gmail Integration</h3>
          <p className="text-sm text-gray-600 mt-1">
            {isAnonymousUser
              ? 'Sign up with an email to connect your Gmail account'
              : 'Connect your Gmail account to search and analyze your emails'
            }
          </p>
        </div>
        <div className="flex items-center">
          {gmailStatus.authenticated ? (
            <div className="flex items-center text-green-600">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Connected
            </div>
          ) : (
            <div className="flex items-center text-gray-500">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              Not Connected
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {gmailStatus.authenticated ? (
          <div className="space-y-3">
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <div className="flex">
                <svg className="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-green-800">Gmail Connected</h4>
                  <p className="text-sm text-green-700 mt-1">
                    You can now ask questions about your emails. Try asking "Show me my recent emails" or "Find emails from last week".
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-md p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Example Queries:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• "Show me my unread emails"</li>
                <li>• "Find <NAME_EMAIL>"</li>
                <li>• "What emails did I receive about meetings?"</li>
                <li>• "Show me emails from last week"</li>
                <li>• "Find emails with attachments"</li>
              </ul>
            </div>

            <button
              onClick={handleDisconnectGmail}
              disabled={isDisconnecting}
              className="w-full px-4 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDisconnecting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-700 mr-2"></div>
                  Disconnecting...
                </div>
              ) : (
                'Disconnect Gmail'
              )}
            </button>
          </div>
        ) : isAnonymousUser ? (
          <div className="space-y-3">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="flex">
                <svg className="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Account Required</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Gmail integration requires a registered account. Please sign up with an email address to connect your Gmail.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-md p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Why sign up?</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Securely connect your Gmail account</li>
                <li>• Search through your emails with natural language</li>
                <li>• Get personalized email insights and summaries</li>
                <li>• Access your email data across sessions</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex">
                <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-blue-800">How to get started</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Sign out and create an account with your email address, then return here to connect your Gmail.
                  </p>
                </div>
              </div>
            </div>

            <button
              disabled={true}
              className="w-full px-4 py-2 text-sm font-medium text-gray-500 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed"
            >
              <div className="flex items-center justify-center">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                Gmail Connection Disabled
              </div>
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex">
                <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-blue-800">Connect Your Gmail</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Connect your Gmail account to enable email search and analysis. We only request read-only access to your emails.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-md p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">What you can do:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Search through your emails with natural language</li>
                <li>• Find specific emails by sender, subject, or content</li>
                <li>• Get summaries of email conversations</li>
                <li>• Analyze email patterns and trends</li>
              </ul>
            </div>

            <button
              onClick={handleConnectGmail}
              disabled={isConnecting}
              className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isConnecting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Connecting...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                  </svg>
                  Connect with Google
                </div>
              )}
            </button>
          </div>
        )}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          <strong>Privacy:</strong> We only request read-only access to your emails. Your email data is processed securely and never stored permanently.
        </p>
      </div>
    </div>
  );
}
