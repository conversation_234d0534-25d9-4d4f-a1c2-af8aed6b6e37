@import url('https://fonts.googleapis.com/css2?family=Forum:wght@400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Warm Orange Theme - Mild and sophisticated */
  --color-light: #FEFCFA; /* Warm white background */
  --color-dark: #3D2914; /* Dark warm brown for text */
  --color-accent: #FF9566; /* Main warm orange */
  --color-cream: #FBF7F2; /* Warm cream */
  --color-white: #FEFCFA; /* Warm white */
  --color-background: #FEFCFA; /* Main background - warm white */
  --color-secondary: #F5F0E8; /* Secondary background - light warm beige */
  --color-peach: #FFB088; /* Light orange for accents */
  --color-orange: #FF9566; /* Primary orange accent */

  /* Warm neutral scale - replacing grays with warm tones */
  --color-neutral-50: #FEFCFA;   /* Warm white */
  --color-neutral-100: #FBF7F2;  /* Very light warm beige */
  --color-neutral-200: #F5F0E8;  /* Light warm beige */
  --color-neutral-300: #EDE4D8;  /* Soft warm beige */
  --color-neutral-400: #E0D2C1;  /* Medium warm beige */
  --color-neutral-500: #D1BFA8;  /* Warm taupe */
  --color-neutral-600: #BFA88C;  /* Deeper taupe */
  --color-neutral-700: #A68E6F;  /* Warm brown */
  --color-neutral-800: #8B7355;  /* Deep warm brown */
  --color-neutral-900: #6F5A3E;  /* Darkest warm brown */

  /* Enhanced orange palette */
  --color-accent-light: #FFB088;  /* Light orange */
  --color-accent-dark: #E8804D;   /* Deeper orange */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;

  /* Typography scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */

  /* Line heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* Spacing scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */

  /* Border radius scale */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: "Forum", serif;
  color: var(--color-dark);
  background: var(--color-background); /* Warm white background */
  line-height: 1.2;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Quasari brand styles - Warm Orange Theme */
.quasari-bg {
  background: var(--color-background); /* Warm white background */
}

.quasari-orange {
  background: var(--color-accent); /* Warm orange accent */
}

.quasari-orange-section {
  background: linear-gradient(135deg, #FF9566 0%, #FFB088 100%);
}

.quasari-card {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 32px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.quasari-card-hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.quasari-button {
  font-family: "Forum", serif;
  background: #000000;
  color: #FFFFFF;
  border-radius: 32px;
  font-weight: 400; /* Forum only has regular weight */
  transition: all 0.15s ease;
  border: none;
  font-size: 16px;
  letter-spacing: -0.01em;
}

.quasari-button:hover {
  background: #1a1a1a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quasari-button-orange {
  font-family: "Forum", serif;
  background: var(--color-accent); /* Warm orange */
  color: #000000;
  border-radius: 32px;
  font-weight: 400; /* Forum only has regular weight */
  transition: all 0.15s ease;
  border: none;
  font-size: 16px;
  letter-spacing: -0.01em;
}

.quasari-button-orange:hover {
  background: var(--color-accent-dark); /* Deeper orange on hover */
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 149, 102, 0.3);
}

/* Typography matching reference images exactly */
.quasari-heading {
  font-family: "Forum", serif;
  font-weight: 400; /* Forum only has regular weight */
  color: #000000;
  line-height: 0.9;
  letter-spacing: -0.04em;
  text-transform: uppercase;
}

.quasari-text {
  font-family: "Forum", serif;
  color: #000000;
  line-height: 1.4;
  font-weight: 400;
}

.quasari-text-muted {
  font-family: "Forum", serif;
  color: #666666;
  line-height: 1.5;
  font-weight: 400;
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-6 py-4 outline-none transition-all duration-200 font-normal;
  font-family: "Forum", serif;
  border-radius: 32px;
  letter-spacing: -0.01em;
  border: 1px solid var(--color-neutral-300);
  color: var(--color-dark);
  background-color: #FFFFFF;
  font-size: 16px;
  line-height: 1.5;
  min-height: 56px;
}

.auth-input-field::placeholder {
  color: var(--color-neutral-600);
  opacity: 1;
}

.auth-input-field:focus {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px rgba(255, 149, 102, 0.1);
  background-color: #FFFFFF;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-6 py-4 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg;
  font-family: "Forum", serif;
  font-weight: 400; /* Forum only has regular weight */
  border-radius: 24px;
}

/* Chat interface specific styles matching reference images */
.chat-message-user {
  background: #FFB366; /* Orange/peach from reference user bubbles */
  border-radius: 32px 32px 8px 32px;
  color: #000000;
}

.chat-message-assistant {
  background: #FFFFFF; /* White assistant bubbles from reference */
  border-radius: 32px 32px 32px 8px;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

/* Scrollbar styling for chat and sidebar */
.chat-scroll::-webkit-scrollbar,
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track,
.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scroll::-webkit-scrollbar-thumb,
.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover,
.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Ensure independent scrolling */
.chat-scroll {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

.sidebar-scroll {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}
