const { fontFamily } = require("tailwindcss/defaultTheme");

module.exports = {
  mode: "jit",
  purge: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Forum", "serif"],
        serif: ["Forum", "serif"],
      },
      borderRadius: {
        DEFAULT: "24px",
        sm: "12px",
        md: "16px",
        lg: "20px",
        xl: "24px",
        "2xl": "32px",
        "3xl": "40px",
        full: "9999px",
      },
      boxShadow: {
        DEFAULT: "0 1px 3px rgba(0, 0, 0, 0.1)",
        sm: "0 1px 2px rgba(0, 0, 0, 0.05)",
        md: "0 4px 6px rgba(0, 0, 0, 0.07)",
        lg: "0 10px 15px rgba(0, 0, 0, 0.1)",
        xl: "0 20px 25px rgba(0, 0, 0, 0.1)",
        "2xl": "0 25px 50px rgba(0, 0, 0, 0.15)",
        none: "none",
      },
      colors: {
        // Colors matching the reference images exactly
        cream: {
          50: "#FEFEFE",
          100: "#FCFCFC",
          200: "#F9F9F9",
          300: "#F7F7F7",
          400: "#F6F4F1",
          500: "#F5F3F0", // Main cream background
          600: "#F0EDE8",
          700: "#EBE8E3",
          800: "#E6E3DE",
          900: "#E1DED9",
        },
        // Light blue/teal background from chat interface image
        skyblue: {
          50: "#F0F9FF",
          100: "#E0F2FE",
          200: "#BAE6FD",
          300: "#7DD3FC",
          400: "#38BDF8",
          500: "#87CEEB", // Main sky blue background from reference
          600: "#0EA5E9",
          700: "#0284C7",
          800: "#0369A1",
          900: "#0C4A6E",
        },
        // Purple/lavender for modal background
        lavender: {
          50: "#FAF5FF",
          100: "#F3E8FF",
          200: "#E9D5FF",
          300: "#D8B4FE",
          400: "#C084FC",
          500: "#C8A2C8", // Main lavender from cookie modal
          600: "#A855F7",
          700: "#9333EA",
          800: "#7C3AED",
          900: "#6B21A8",
        },
        // Yellow/lime green accent
        yellow: {
          DEFAULT: "#E8FF59", // Exact bright yellow-green from reference
          50: "#FEFFFC",
          100: "#FEFFF9",
          200: "#FDFFEF",
          300: "#FCFFE5",
          400: "#F5FFB0",
          500: "#E8FF59", // Main yellow-green from reference
          600: "#E0F751",
          700: "#D8EF49",
          800: "#D0E741",
          900: "#C8DF39",
        },
        // Orange/peach for user message bubbles
        peach: {
          50: "#FFF7ED",
          100: "#FFEDD5",
          200: "#FED7AA",
          300: "#FDBA74",
          400: "#FB923C",
          500: "#FFB366", // Main peach/orange from user bubbles
          600: "#EA580C",
          700: "#C2410C",
          800: "#9A3412",
          900: "#7C2D12",
        },
        dark: {
          DEFAULT: "#000000", // Pure black from reference
          50: "#F7F7F7",
          100: "#E3E3E3",
          200: "#C8C8C8",
          300: "#A4A4A4",
          400: "#818181",
          500: "#666666",
          600: "#515151",
          700: "#434343",
          800: "#383838",
          900: "#000000", // Pure black
        },
        gray: {
          50: "#FAFAFA",
          100: "#F5F5F5",
          200: "#EEEEEE",
          300: "#E0E0E0",
          400: "#BDBDBD",
          500: "#9E9E9E",
          600: "#757575",
          700: "#616161",
          800: "#424242",
          900: "#212121",
        },
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1.1' }],
        '6xl': ['3.75rem', { lineHeight: '1.1' }],
        '7xl': ['4.5rem', { lineHeight: '1.1' }],
        '8xl': ['6rem', { lineHeight: '1.1' }],
        '9xl': ['8rem', { lineHeight: '1.1' }],
      },
      spacing: {
        "18": "4.5rem",
        "88": "22rem",
        "128": "32rem",
      },
    },
  },
  variants: {
    extend: {
      boxShadow: ["hover", "active"],
    },
  },
};
