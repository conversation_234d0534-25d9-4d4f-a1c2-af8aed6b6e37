import { query, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getUserSearches = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("searches")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(20);
  },
});

export const insertSearch = internalMutation({
  args: {
    userId: v.id("users"),
    sessionId: v.optional(v.string()),
    query: v.string(),
    results: v.string(),
    aiResponse: v.string(),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("searches", args);
  },
});

export const clearSearchProgress = internalMutation({
  args: {
    searchId: v.string(),
  },
  handler: async (ctx, args) => {
    // Delete any existing progress records for this searchId
    const existing = await ctx.db
      .query("searchProgress")
      .withIndex("by_searchId", (q) => q.eq("searchId", args.searchId))
      .collect();

    for (const record of existing) {
      await ctx.db.delete(record._id);
    }
  },
});

export const updateSearchProgress = internalMutation({
  args: {
    searchId: v.string(),
    step: v.string(),
    progress: v.number(),
    message: v.string(),
    sourceUrl: v.optional(v.string()),
    sourceTitle: v.optional(v.string()),
    finalResult: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Try to find existing progress record
    const existing = await ctx.db
      .query("searchProgress")
      .withIndex("by_searchId", (q) => q.eq("searchId", args.searchId))
      .unique();

    if (existing) {
      // Only update if progress is moving forward or step is changing
      if (args.progress >= existing.progress || args.step !== existing.step) {
        return await ctx.db.patch(existing._id, {
          step: args.step,
          progress: args.progress,
          message: args.message,
          sourceUrl: args.sourceUrl,
          sourceTitle: args.sourceTitle,
          finalResult: args.finalResult,
          updatedAt: Date.now(),
        });
      }
      return existing._id; // Return existing ID if no update needed
    } else {
      // Create new record
      return await ctx.db.insert("searchProgress", {
        searchId: args.searchId,
        step: args.step,
        progress: args.progress,
        message: args.message,
        sourceUrl: args.sourceUrl,
        sourceTitle: args.sourceTitle,
        finalResult: args.finalResult,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

export const getSearchProgress = query({
  args: {
    searchId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("searchProgress")
      .withIndex("by_searchId", (q) => q.eq("searchId", args.searchId))
      .unique();
  },
});

// Clean up old completed search progress records (older than 1 hour)
export const cleanupOldSearchProgress = internalMutation({
  args: {},
  handler: async (ctx) => {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);

    const oldRecords = await ctx.db
      .query("searchProgress")
      .filter((q) =>
        q.and(
          q.eq(q.field("step"), "complete"),
          q.lt(q.field("updatedAt"), oneHourAgo)
        )
      )
      .collect();

    for (const record of oldRecords) {
      await ctx.db.delete(record._id);
    }

    return { deletedCount: oldRecords.length };
  },
});

// Session Management Functions

export const createSession = internalMutation({
  args: {
    userId: v.id("users"),
    sessionId: v.string(),
    title: v.optional(v.string()),
    conversationHistory: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("sessions", {
      userId: args.userId,
      sessionId: args.sessionId,
      title: args.title,
      conversationHistory: args.conversationHistory,
      lastActivity: Date.now(),
      createdAt: Date.now(),
      messageCount: 0,
    });
  },
});

export const updateSession = internalMutation({
  args: {
    sessionId: v.string(),
    conversationHistory: v.string(),
    title: v.optional(v.string()),
    messageCount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_sessionId", (q) => q.eq("sessionId", args.sessionId))
      .unique();

    if (!session) {
      throw new Error("Session not found");
    }

    return await ctx.db.patch(session._id, {
      conversationHistory: args.conversationHistory,
      title: args.title || session.title,
      messageCount: args.messageCount || session.messageCount,
      lastActivity: Date.now(),
    });
  },
});

export const getSession = query({
  args: {
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    return await ctx.db
      .query("sessions")
      .withIndex("by_sessionId", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("userId"), userId))
      .unique();
  },
});

export const getUserSessions = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("sessions")
      .withIndex("by_user_activity", (q) => q.eq("userId", userId))
      .order("desc")
      .take(50);
  },
});

export const deleteSession = internalMutation({
  args: {
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("sessions")
      .withIndex("by_sessionId", (q) => q.eq("sessionId", args.sessionId))
      .unique();

    if (session) {
      await ctx.db.delete(session._id);
    }
  },
});

export const getSessionForUser = internalMutation({
  args: {
    sessionId: v.string(),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sessions")
      .withIndex("by_sessionId", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .unique();
  },
});
