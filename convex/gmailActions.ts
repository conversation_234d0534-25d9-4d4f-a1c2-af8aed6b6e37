import { action, query, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal, api } from "./_generated/api";

// Gmail OAuth2 credentials (these should be set in Convex environment variables)
const GMAIL_OAUTH_CONFIG = {
  client_id: process.env.GMAIL_CLIENT_ID || "your_client_id_here",
  client_secret: process.env.GMAIL_CLIENT_SECRET || "your_client_secret_here",
  redirect_uri: process.env.GMAIL_REDIRECT_URI || "http://localhost:3000"
};

// Helper function to extract email headers
function getHeaderValue(headers: any[], name: string): string {
  const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
  return header ? header.value : '';
}

// Helper function to extract email body (simplified)
function extractEmailBody(payload: any): string {
  let body = '';

  if (payload.body && payload.body.data) {
    try {
      body = atob(payload.body.data.replace(/-/g, '+').replace(/_/g, '/'));
    } catch (e) {
      body = payload.body.data;
    }
  } else if (payload.parts) {
    for (const part of payload.parts) {
      if (part.mimeType === 'text/plain' && part.body && part.body.data) {
        try {
          body = atob(part.body.data.replace(/-/g, '+').replace(/_/g, '/'));
          break;
        } catch (e) {
          body = part.body.data;
          break;
        }
      } else if (part.mimeType === 'text/html' && part.body && part.body.data && !body) {
        try {
          body = atob(part.body.data.replace(/-/g, '+').replace(/_/g, '/'));
        } catch (e) {
          body = part.body.data;
        }
      }
    }
  }

  return body;
}

// Get Gmail authentication status for current user
export const getGmailAuthStatus = query({
  args: {},
  handler: async (ctx): Promise<{
    authenticated: boolean;
    hasCredentials: boolean;
    lastUpdated?: number;
    error?: string;
  }> => {
    console.log('[GMAIL STATUS] Checking Gmail auth status...');

    const userId = await getAuthUserId(ctx);
    console.log('[GMAIL STATUS] User ID from auth:', userId);

    if (!userId) {
      console.log('[GMAIL STATUS] No authenticated user found');
      return { authenticated: false, hasCredentials: false, error: "User not authenticated" };
    }

    const credentials = await ctx.runQuery(internal.gmailActions.getGmailCredentials, {
      userId
    });

    console.log('[GMAIL STATUS] Credentials found:', !!credentials);

    return {
      authenticated: !!credentials,
      hasCredentials: !!credentials,
      lastUpdated: credentials?.updatedAt
    };
  },
});

// Generate Gmail OAuth2 authorization URL
export const getGmailAuthUrl = action({
  args: {},
  handler: async (ctx): Promise<{
    authUrl: string;
    success: boolean;
  }> => {
    console.log('[GMAIL AUTH URL] Starting auth URL generation...');

    const userId = await getAuthUserId(ctx);
    console.log('[GMAIL AUTH URL] User ID from auth:', userId);

    if (!userId) {
      console.error('[GMAIL AUTH URL] No authenticated user found');
      throw new Error("User must be authenticated");
    }

    // Get user information to check if they're anonymous
    const user = await ctx.runQuery(api.auth.loggedInUser);
    console.log('[GMAIL AUTH URL] User info:', {
      userId,
      email: user?.email || 'no-email',
      isAnonymous: !user?.email || user.email === 'anonymous'
    });

    // Prevent anonymous users from accessing Gmail OAuth
    if (!user?.email || user.email === 'anonymous') {
      console.error('[GMAIL AUTH URL] Anonymous user attempted Gmail OAuth');
      throw new Error("Gmail integration requires a registered account. Please sign up with an email address.");
    }

    try {
      const scopes = 'https://www.googleapis.com/auth/gmail.readonly';
      const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${encodeURIComponent(GMAIL_OAUTH_CONFIG.client_id)}&` +
        `redirect_uri=${encodeURIComponent(GMAIL_OAUTH_CONFIG.redirect_uri)}&` +
        `response_type=code&` +
        `scope=${encodeURIComponent(scopes)}&` +
        `access_type=offline&` +
        `prompt=consent`;

      console.log('[GMAIL AUTH URL] Generated auth URL successfully', {
        userId,
        userEmail: user.email,
        clientId: GMAIL_OAUTH_CONFIG.client_id,
        redirectUri: GMAIL_OAUTH_CONFIG.redirect_uri
      });

      return {
        authUrl,
        success: true
      };
    } catch (error) {
      console.error("[GMAIL AUTH URL] Error generating auth URL:", error);
      throw new Error(`Failed to generate Gmail auth URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Handle OAuth2 callback and store credentials with user ID
export const handleGmailCallbackWithUserId = action({
  args: {
    code: v.string(),
    userId: v.string(),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
  }> => {
    console.log('[GMAIL CALLBACK] Starting callback processing with user ID...', {
      codeLength: args.code.length,
      providedUserId: args.userId,
      timestamp: Date.now()
    });

    try {
      console.log('[GMAIL CALLBACK] Exchanging authorization code for tokens...');

      // Exchange authorization code for tokens
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GMAIL_OAUTH_CONFIG.client_id,
          client_secret: GMAIL_OAUTH_CONFIG.client_secret,
          code: args.code,
          grant_type: 'authorization_code',
          redirect_uri: GMAIL_OAUTH_CONFIG.redirect_uri,
        }),
      });

      console.log('[GMAIL CALLBACK] Token exchange response status:', tokenResponse.status);

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.error('[GMAIL CALLBACK] Token exchange failed:', {
          status: tokenResponse.status,
          error: errorText
        });
        throw new Error(`Token exchange failed: ${tokenResponse.status} ${errorText}`);
      }

      const tokens = await tokenResponse.json();
      console.log('[GMAIL CALLBACK] Tokens received successfully', {
        hasAccessToken: !!tokens.access_token,
        hasRefreshToken: !!tokens.refresh_token,
        expiresIn: tokens.expires_in
      });

      // Store credentials in database using the provided user ID
      console.log('[GMAIL CALLBACK] Storing credentials in database...', { userId: args.userId });
      await ctx.runMutation(internal.gmailActions.storeGmailCredentials, {
        userId: args.userId as any, // Cast to the expected ID type
        credentials: JSON.stringify(GMAIL_OAUTH_CONFIG),
        refreshToken: tokens.refresh_token || "",
        accessToken: tokens.access_token || "",
        expiryDate: tokens.expires_in ? Date.now() + (tokens.expires_in * 1000) : undefined,
      });

      console.log('[GMAIL CALLBACK] Credentials stored successfully');

      return {
        success: true,
        message: "Gmail authentication successful"
      };
    } catch (error) {
      console.error("[GMAIL CALLBACK] Error handling callback:", error);
      throw new Error(`Gmail authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Handle OAuth2 callback and store credentials (original method)
export const handleGmailCallback = action({
  args: {
    code: v.string(),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
  }> => {
    console.log('[GMAIL CALLBACK] Starting callback processing...', {
      codeLength: args.code.length,
      timestamp: Date.now()
    });

    const userId = await getAuthUserId(ctx);
    console.log('[GMAIL CALLBACK] User ID from auth:', userId);

    if (!userId) {
      console.error('[GMAIL CALLBACK] No authenticated user found during callback!');
      throw new Error("User must be authenticated");
    }

    // Get user information to check if they're anonymous
    const user = await ctx.runQuery(api.auth.loggedInUser);
    console.log('[GMAIL CALLBACK] User info:', {
      userId,
      email: user?.email || 'no-email',
      isAnonymous: !user?.email || user.email === 'anonymous'
    });

    // Prevent anonymous users from completing Gmail OAuth
    if (!user?.email || user.email === 'anonymous') {
      console.error('[GMAIL CALLBACK] Anonymous user attempted Gmail OAuth callback');
      throw new Error("Gmail integration requires a registered account. Please sign up with an email address.");
    }

    try {
      console.log('[GMAIL CALLBACK] Exchanging authorization code for tokens...');

      // Exchange authorization code for tokens
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GMAIL_OAUTH_CONFIG.client_id,
          client_secret: GMAIL_OAUTH_CONFIG.client_secret,
          code: args.code,
          grant_type: 'authorization_code',
          redirect_uri: GMAIL_OAUTH_CONFIG.redirect_uri,
        }),
      });

      console.log('[GMAIL CALLBACK] Token exchange response status:', tokenResponse.status);

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.error('[GMAIL CALLBACK] Token exchange failed:', {
          status: tokenResponse.status,
          error: errorText
        });
        throw new Error(`Token exchange failed: ${tokenResponse.status} ${errorText}`);
      }

      const tokens = await tokenResponse.json();
      console.log('[GMAIL CALLBACK] Tokens received successfully', {
        hasAccessToken: !!tokens.access_token,
        hasRefreshToken: !!tokens.refresh_token,
        expiresIn: tokens.expires_in
      });

      // Store credentials in database
      console.log('[GMAIL CALLBACK] Storing credentials in database...', { userId });
      await ctx.runMutation(internal.gmailActions.storeGmailCredentials, {
        userId,
        credentials: JSON.stringify(GMAIL_OAUTH_CONFIG),
        refreshToken: tokens.refresh_token || "",
        accessToken: tokens.access_token || "",
        expiryDate: tokens.expires_in ? Date.now() + (tokens.expires_in * 1000) : undefined,
      });

      console.log('[GMAIL CALLBACK] Credentials stored successfully');

      return {
        success: true,
        message: "Gmail authentication successful"
      };
    } catch (error) {
      console.error("[GMAIL CALLBACK] Error handling callback:", error);
      throw new Error(`Gmail authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Disconnect Gmail account
export const disconnectGmail = action({
  args: {},
  handler: async (ctx): Promise<{
    success: boolean;
    message: string;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
        userId
      });

      return {
        success: true,
        message: "Gmail account disconnected successfully"
      };
    } catch (error) {
      console.error("[GMAIL] Error disconnecting:", error);
      throw new Error(`Failed to disconnect Gmail: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Fetch Gmail emails with explicit userId (for internal actions)
export const fetchGmailEmailsWithUserId = action({
  args: {
    query: v.string(),
    maxResults: v.optional(v.number()),
    includeBody: v.optional(v.boolean()),
    userId: v.id("users"),
  },
  handler: async (ctx, args): Promise<{
    emails: Array<{
      id: string;
      threadId: string;
      subject: string;
      from: string;
      to: string;
      date: string;
      snippet: string;
      body: string | null;
    }>;
    totalCount: number;
    query: string;
    maxResults: number;
    success?: boolean;
    message?: string;
    authRequired?: boolean;
  }> => {
    // Use the provided userId instead of getting it from auth context
    const userId = args.userId;

    try {
      // Get stored credentials
      const storedCredentials = await ctx.runQuery(internal.gmailActions.getGmailCredentials, {
        userId
      });

      if (!storedCredentials) {
        return {
          emails: [],
          totalCount: 0,
          message: "Gmail not connected. Please authenticate your Gmail account first.",
          authRequired: true,
          query: args.query,
          maxResults: args.maxResults || 10
        };
      }

      let accessToken = storedCredentials.accessToken;

      // Check if token needs refresh
      if (storedCredentials.expiryDate && Date.now() >= storedCredentials.expiryDate) {
        console.log('[GMAIL] Access token expired, refreshing...');

        try {
          const refreshResponse = await fetch('https://oauth2.googleapis.com/token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              client_id: GMAIL_OAUTH_CONFIG.client_id,
              client_secret: GMAIL_OAUTH_CONFIG.client_secret,
              refresh_token: storedCredentials.refreshToken,
              grant_type: 'refresh_token',
            }),
          });

          if (!refreshResponse.ok) {
            const errorText = await refreshResponse.text();
            console.error('[GMAIL] Token refresh failed:', {
              status: refreshResponse.status,
              statusText: refreshResponse.statusText,
              error: errorText
            });

            // If refresh token is invalid, delete credentials and require re-auth
            if (refreshResponse.status === 400 || errorText.includes('invalid_grant')) {
              console.log('[GMAIL] Refresh token invalid, deleting credentials');
              await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
                userId
              });

              return {
                emails: [],
                totalCount: 0,
                message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
                authRequired: true,
                query: args.query,
                maxResults: args.maxResults || 10
              };
            }

            throw new Error(`Failed to refresh Gmail access token: ${refreshResponse.status} ${errorText}`);
          }

          const refreshTokens = await refreshResponse.json();
          accessToken = refreshTokens.access_token;

          // Update stored credentials with new tokens
          await ctx.runMutation(internal.gmailActions.storeGmailCredentials, {
            userId,
            credentials: storedCredentials.credentials,
            refreshToken: refreshTokens.refresh_token || storedCredentials.refreshToken,
            accessToken: accessToken,
            expiryDate: refreshTokens.expires_in ? Date.now() + (refreshTokens.expires_in * 1000) : undefined,
          });

          console.log('[GMAIL] Access token refreshed successfully');
        } catch (refreshError) {
          console.error('[GMAIL] Error during token refresh:', refreshError);

          // Delete invalid credentials and require re-auth
          await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
            userId
          });

          return {
            emails: [],
            totalCount: 0,
            message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
            authRequired: true,
            query: args.query,
            maxResults: args.maxResults || 10
          };
        }
      }

      const maxResults = Math.min(args.maxResults || 10, 50);

      console.log(`[GMAIL] Fetching emails with query: "${args.query}"`);

      // Search for messages using Gmail API
      const messageListResponse = await fetch(
        `https://gmail.googleapis.com/gmail/v1/users/me/messages?q=${encodeURIComponent(args.query)}&maxResults=${maxResults}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!messageListResponse.ok) {
        throw new Error(`Gmail API error: ${messageListResponse.status} ${messageListResponse.statusText}`);
      }

      const messageList = await messageListResponse.json();
      const emails = [];

      if (messageList.messages) {
        for (const message of messageList.messages) {
          try {
            const messageResponse = await fetch(
              `https://gmail.googleapis.com/gmail/v1/users/me/messages/${message.id}?format=${args.includeBody ? 'full' : 'metadata'}`,
              {
                headers: {
                  'Authorization': `Bearer ${accessToken}`,
                  'Content-Type': 'application/json',
                },
              }
            );

            if (messageResponse.ok) {
              const messageData = await messageResponse.json();
              const payload = messageData.payload || {};
              const headers = payload.headers || [];

              const email = {
                id: messageData.id || '',
                threadId: messageData.threadId || '',
                subject: getHeaderValue(headers, 'Subject'),
                from: getHeaderValue(headers, 'From'),
                to: getHeaderValue(headers, 'To'),
                date: getHeaderValue(headers, 'Date'),
                snippet: messageData.snippet || '',
                body: args.includeBody ? extractEmailBody(payload) : null,
              };

              emails.push(email);
            }
          } catch (error) {
            console.error(`[GMAIL] Error fetching message ${message.id}:`, error);
            // Continue with other messages
          }
        }
      }

      console.log(`[GMAIL] Successfully fetched ${emails.length} emails`);

      return {
        emails,
        totalCount: messageList.resultSizeEstimate || 0,
        query: args.query,
        maxResults: maxResults,
        success: true
      };

    } catch (error) {
      console.error("[GMAIL] Error fetching emails:", error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('invalid_grant')) {
        // Delete invalid credentials
        await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
          userId
        });

        return {
          emails: [],
          totalCount: 0,
          message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
          authRequired: true,
          query: args.query,
          maxResults: args.maxResults || 10
        };
      }

      throw new Error(`Gmail fetch failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Fetch Gmail emails (updated implementation)
export const fetchGmailEmails = action({
  args: {
    query: v.string(),
    maxResults: v.optional(v.number()),
    includeBody: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{
    emails: Array<{
      id: string;
      threadId: string;
      subject: string;
      from: string;
      to: string;
      date: string;
      snippet: string;
      body: string | null;
    }>;
    totalCount: number;
    query: string;
    maxResults: number;
    success?: boolean;
    message?: string;
    authRequired?: boolean;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      // Get stored credentials
      const storedCredentials = await ctx.runQuery(internal.gmailActions.getGmailCredentials, {
        userId
      });

      if (!storedCredentials) {
        return {
          emails: [],
          totalCount: 0,
          message: "Gmail not connected. Please authenticate your Gmail account first.",
          authRequired: true,
          query: args.query,
          maxResults: args.maxResults || 10
        };
      }

      let accessToken = storedCredentials.accessToken;

      // Check if token needs refresh
      if (storedCredentials.expiryDate && Date.now() >= storedCredentials.expiryDate) {
        console.log('[GMAIL] Access token expired, refreshing...');

        try {
          const refreshResponse = await fetch('https://oauth2.googleapis.com/token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              client_id: GMAIL_OAUTH_CONFIG.client_id,
              client_secret: GMAIL_OAUTH_CONFIG.client_secret,
              refresh_token: storedCredentials.refreshToken,
              grant_type: 'refresh_token',
            }),
          });

          if (!refreshResponse.ok) {
            const errorText = await refreshResponse.text();
            console.error('[GMAIL] Token refresh failed:', {
              status: refreshResponse.status,
              statusText: refreshResponse.statusText,
              error: errorText
            });

            // If refresh token is invalid, delete credentials and require re-auth
            if (refreshResponse.status === 400 || errorText.includes('invalid_grant')) {
              console.log('[GMAIL] Refresh token invalid, deleting credentials');
              await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
                userId
              });

              return {
                emails: [],
                totalCount: 0,
                message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
                authRequired: true,
                query: args.query,
                maxResults: args.maxResults || 10
              };
            }

            throw new Error(`Failed to refresh Gmail access token: ${refreshResponse.status} ${errorText}`);
          }

          const refreshTokens = await refreshResponse.json();
          accessToken = refreshTokens.access_token;

          // Update stored credentials with new tokens
          await ctx.runMutation(internal.gmailActions.storeGmailCredentials, {
            userId,
            credentials: storedCredentials.credentials,
            refreshToken: refreshTokens.refresh_token || storedCredentials.refreshToken,
            accessToken: accessToken,
            expiryDate: refreshTokens.expires_in ? Date.now() + (refreshTokens.expires_in * 1000) : undefined,
          });

          console.log('[GMAIL] Access token refreshed successfully');
        } catch (refreshError) {
          console.error('[GMAIL] Error during token refresh:', refreshError);

          // Delete invalid credentials and require re-auth
          await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
            userId
          });

          return {
            emails: [],
            totalCount: 0,
            message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
            authRequired: true,
            query: args.query,
            maxResults: args.maxResults || 10
          };
        }
      }

      const maxResults = Math.min(args.maxResults || 10, 50);

      console.log(`[GMAIL] Fetching emails with query: "${args.query}"`);

      // Search for messages using Gmail API
      const messageListResponse = await fetch(
        `https://gmail.googleapis.com/gmail/v1/users/me/messages?q=${encodeURIComponent(args.query)}&maxResults=${maxResults}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!messageListResponse.ok) {
        throw new Error(`Gmail API error: ${messageListResponse.status} ${messageListResponse.statusText}`);
      }

      const messageList = await messageListResponse.json();
      const emails = [];

      if (messageList.messages) {
        for (const message of messageList.messages) {
          try {
            const messageResponse = await fetch(
              `https://gmail.googleapis.com/gmail/v1/users/me/messages/${message.id}?format=${args.includeBody ? 'full' : 'metadata'}`,
              {
                headers: {
                  'Authorization': `Bearer ${accessToken}`,
                  'Content-Type': 'application/json',
                },
              }
            );

            if (messageResponse.ok) {
              const messageData = await messageResponse.json();
              const payload = messageData.payload || {};
              const headers = payload.headers || [];

              const email = {
                id: messageData.id || '',
                threadId: messageData.threadId || '',
                subject: getHeaderValue(headers, 'Subject'),
                from: getHeaderValue(headers, 'From'),
                to: getHeaderValue(headers, 'To'),
                date: getHeaderValue(headers, 'Date'),
                snippet: messageData.snippet || '',
                body: args.includeBody ? extractEmailBody(payload) : null,
              };

              emails.push(email);
            }
          } catch (error) {
            console.error(`[GMAIL] Error fetching message ${message.id}:`, error);
            // Continue with other messages
          }
        }
      }

      console.log(`[GMAIL] Successfully fetched ${emails.length} emails`);

      return {
        emails,
        totalCount: messageList.resultSizeEstimate || 0,
        query: args.query,
        maxResults: maxResults,
        success: true
      };

    } catch (error) {
      console.error("[GMAIL] Error fetching emails:", error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('invalid_grant')) {
        // Delete invalid credentials
        await ctx.runMutation(internal.gmailActions.deleteGmailCredentials, {
          userId
        });

        return {
          emails: [],
          totalCount: 0,
          message: "Gmail authentication expired. Please re-authenticate your Gmail account.",
          authRequired: true,
          query: args.query,
          maxResults: args.maxResults || 10
        };
      }

      throw new Error(`Gmail fetch failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Internal functions for Gmail credential management
export const storeGmailCredentials = internalMutation({
  args: {
    userId: v.id("users"),
    credentials: v.string(), // JSON string of OAuth2 credentials
    refreshToken: v.string(),
    accessToken: v.string(),
    expiryDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Check if credentials already exist for this user
    const existing = await ctx.db
      .query("gmailCredentials")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (existing) {
      // Update existing credentials
      return await ctx.db.patch(existing._id, {
        credentials: args.credentials,
        refreshToken: args.refreshToken,
        accessToken: args.accessToken,
        expiryDate: args.expiryDate,
        updatedAt: Date.now(),
      });
    } else {
      // Create new credentials record
      return await ctx.db.insert("gmailCredentials", {
        userId: args.userId,
        credentials: args.credentials,
        refreshToken: args.refreshToken,
        accessToken: args.accessToken,
        expiryDate: args.expiryDate,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

export const getGmailCredentials = internalQuery({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("gmailCredentials")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();
  },
});

export const deleteGmailCredentials = internalMutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const credentials = await ctx.db
      .query("gmailCredentials")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (credentials) {
      await ctx.db.delete(credentials._id);
    }
  },
});
