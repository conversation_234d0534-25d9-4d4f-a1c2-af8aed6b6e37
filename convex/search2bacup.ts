import { GoogleGenAI } from '@google/genai';
import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// ============================================================================
// CONFIGURATION VARIABLES - EASILY CUSTOMIZABLE
// ============================================================================

// API Keys
const SERPER_API_KEY = "a7f3bf8670831828db85f93f30d7b34a2c755767";
const GEMINI_API_KEY = "AIzaSyCgsPrZ70Vo_sVH0_bPq0rb_BYl83CHnuE";

// AI Model Configuration
const AI_MODELS = {
  MAIN_MODEL: "gemini-2.0-flash",           // Primary model for search and function calling
  EVALUATION_MODEL: "gemini-2.0-flash-lite", // Lighter model for round evaluation
  FINAL_RESPONSE_MODEL: "gemini-2.0-flash"   // Model for generating final responses
};

// Search Round Configuration
const SEARCH_ROUNDS = {
  ABSOLUTE_MAX_ROUNDS: 20,        // Hard limit to prevent infinite loops
  GUARANTEED_ROUNDS: 3,           // Always continue for first N rounds
  FALLBACK_MAX_ROUNDS: 8,         // Fallback limit if AI evaluation fails
  AI_CONFIDENCE_THRESHOLD: 6      // Minimum confidence (1-10) to continue rounds
};

// Progress Tracking Configuration
const PROGRESS_CONFIG = {
  INITIAL_PROGRESS: 10,           // Starting progress percentage
  ROUND_PROGRESS_INCREMENT: 15,   // Progress increase per round
  MAX_ROUND_PROGRESS: 80,         // Maximum progress from rounds
  SCRAPING_BASE_PROGRESS: 40,     // Base progress when scraping starts
  SCRAPING_INCREMENT: 10,         // Progress increase per scraped source
  MAX_SCRAPING_PROGRESS: 75,      // Maximum progress from scraping
  FINALIZING_PROGRESS: 90,        // Progress when generating final response
  COMPLETE_PROGRESS: 100          // Final completion progress
};

// Search Step Thresholds
const STEP_THRESHOLDS = {
  SEARCHING_ROUNDS: 2,            // Rounds 1-2: "searching"
  ANALYZING_ROUNDS: 4,            // Rounds 3-4: "analyzing"
  // Rounds 5+: "processing"
};

// Default Search Parameters
const SEARCH_DEFAULTS = {
  NUM_RESULTS: 10,                // Default number of search results
  REGION: 'us',                   // Default region code
  LANGUAGE: 'en'                  // Default language code
};

// Session Configuration
const SESSION_CONFIG = {
  TITLE_MAX_LENGTH: 50,           // Maximum length for auto-generated session titles
  TITLE_TRUNCATE_SUFFIX: "..."    // Suffix for truncated titles
};

// Function Calling Configuration
const FUNCTION_CONFIG = {
  MODE: 'ANY'                     // Function calling mode for Gemini
};

// Gmail Configuration
const GMAIL_CONFIG = {
  DEFAULT_MAX_RESULTS: 10,        // Default number of emails to fetch
  MAX_RESULTS_LIMIT: 50,          // Maximum allowed emails per request
  DEFAULT_INCLUDE_BODY: true,     // Include email body by default
  SCOPES: [                       // Required Gmail API scopes
    'https://www.googleapis.com/auth/gmail.readonly'
  ]
};

// Configure the AI client
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

// Define function declarations for Gemini (exactly as in your code)
const functionDeclarations = [
  {
    name: "search_web",
    description: "Search the web using Serper API to get current information",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query"
        },
        num: {
          type: "number",
          description: "Number of results to return (default: 10)"
        },
        gl: {
          type: "string",
          description: "Region code (default: 'us')"
        },
        hl: {
          type: "string",
          description: "Language code (default: 'en')"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "scrape_webpage",
    description: "Scrape content from a specific webpage URL",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to scrape"
        }
      },
      required: ["url"]
    }
  },
  {
    name: "fetch_gmail",
    description: "Fetch emails from the user's Gmail account. Use this when the user asks about their emails, recent messages, or specific email content. Requires user authentication.",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to filter emails (e.g., 'from:<EMAIL>', 'subject:meeting', 'is:unread', 'newer_than:7d')"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of emails to fetch (default: 10, max: 50)"
        },
        includeBody: {
          type: "boolean",
          description: "Whether to include email body content (default: true)"
        }
      },
      required: ["query"]
    }
  }
];

// Function to perform web search using Serper API (exactly as in your code)
async function performSerperSearch({
  query,
  num = SEARCH_DEFAULTS.NUM_RESULTS,
  gl = SEARCH_DEFAULTS.REGION,
  hl = SEARCH_DEFAULTS.LANGUAGE
}: any) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API (exactly as in your code)
async function performSerperScrape({ url }: any) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}

// Function to fetch Gmail emails (requires user authentication)
async function performGmailFetch({
  query,
  maxResults = GMAIL_CONFIG.DEFAULT_MAX_RESULTS,
  includeBody = GMAIL_CONFIG.DEFAULT_INCLUDE_BODY,
  userId,
  ctx
}: any) {
  console.log(`[GMAIL] Fetching emails with query: "${query}"`);

  // Validate maxResults
  const validMaxResults = Math.min(maxResults, GMAIL_CONFIG.MAX_RESULTS_LIMIT);

  try {
    // Use the real Gmail implementation
    const result = await ctx.runAction(api.gmailActions.fetchGmailEmails, {
      query: query,
      maxResults: validMaxResults,
      includeBody: includeBody
    });

    console.log(`[GMAIL] Fetched ${result.emails?.length || 0} emails for user ${userId || 'unknown'}`);
    return result;

  } catch (error: any) {
    console.error(`[GMAIL] Error fetching emails:`, error.message);
    throw new Error(`Gmail fetch failed: ${error.message}`);
  }
}

// Global chat session management with request queuing
let globalChat: any = null;
let chatBusy = false;
let requestQueue: Array<{
  resolve: (value: any) => void;
  reject: (error: any) => void;
  operation: () => Promise<any>;
}> = [];

// Initialize or reset the global chat session
function initializeGlobalChat() {
  console.log("[CHAT] Initializing global chat session");
  globalChat = ai.chats.create({
    model: AI_MODELS.MAIN_MODEL,
    config: {
      tools: [{ functionDeclarations: functionDeclarations as any }],
      toolConfig: {
        functionCallingConfig: {
          mode: FUNCTION_CONFIG.MODE as any
        }
      }
    },
  });
}

// Process the request queue
async function processQueue() {
  if (chatBusy || requestQueue.length === 0) {
    return;
  }

  chatBusy = true;
  const request = requestQueue.shift();

  if (request) {
    try {
      const result = await request.operation();
      request.resolve(result);
    } catch (error) {
      console.error("[CHAT] Request failed:", error);
      // If chat session is corrupted, reinitialize it
      if (error instanceof Error && (
        error.message.includes('ContentUnion') ||
        error.message.includes('required oneof field') ||
        error.message.includes('invalid state')
      )) {
        console.log("[CHAT] Chat session corrupted, reinitializing...");
        initializeGlobalChat();
      }
      request.reject(error);
    }
  }

  chatBusy = false;

  // Process next request in queue
  if (requestQueue.length > 0) {
    setTimeout(() => processQueue(), 0);
  }
}

// Queue a chat operation to ensure thread safety
function queueChatOperation<T>(operation: () => Promise<T>): Promise<T> {
  return new Promise((resolve, reject) => {
    requestQueue.push({
      resolve,
      reject,
      operation
    });

    // Start processing if not already busy
    if (!chatBusy) {
      setTimeout(() => processQueue(), 0);
    }
  });
}

// Safe chat message sender with automatic retry
async function sendChatMessage(message: string, disableFunctions: boolean = false): Promise<any> {
  return queueChatOperation(async () => {
    if (!globalChat) {
      initializeGlobalChat();
    }

    try {
      console.log(`[CHAT] Sending message: ${message.substring(0, 100)}...`);

      if (disableFunctions) {
        // Use direct API call without function calling for final responses
        console.log(`[CHAT] Using direct API call (functions disabled)`);
        const response = await ai.models.generateContent({
          model: AI_MODELS.FINAL_RESPONSE_MODEL,
          contents: [{ role: "user", parts: [{ text: message }] }]
        });
        console.log(`[CHAT] Received direct response`);
        return response;
      } else {
        const response = await globalChat.sendMessage({ message: message });
        console.log(`[CHAT] Received response with ${response.functionCalls?.length || 0} function calls`);
        return response;
      }
    } catch (error) {
      console.error("[CHAT] Send message failed:", error);
      // Reinitialize chat on certain errors
      if (error instanceof Error && (
        error.message.includes('ContentUnion') ||
        error.message.includes('required oneof field')
      )) {
        console.log("[CHAT] Reinitializing chat due to error");
        initializeGlobalChat();
        // Retry once with new session
        if (disableFunctions) {
          const response = await ai.models.generateContent({
            model: AI_MODELS.FINAL_RESPONSE_MODEL,
            contents: [{ role: "user", parts: [{ text: message }] }]
          });
          console.log(`[CHAT] Retry successful with direct API`);
          return response;
        } else {
          const response = await globalChat.sendMessage({ message: message });
          console.log(`[CHAT] Retry successful with ${response.functionCalls?.length || 0} function calls`);
          return response;
        }
      }
      throw error;
    }
  });
}

// Initialize the global chat session on startup
initializeGlobalChat();

// Function to evaluate if more rounds are needed using AI (exactly as in your code)
async function shouldContinueRounds(round: number) {
  // Safety mechanism: absolute maximum to prevent infinite loops
  if (round >= SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS) {
    console.log(`Reached absolute maximum of ${SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS} rounds - stopping for safety`);
    return false;
  }

  // For the first few rounds, always continue if there are function calls
  if (round <= SEARCH_ROUNDS.GUARANTEED_ROUNDS) {
    return true;
  }

  // Ask the AI model to evaluate if more rounds are needed
  try {
    const evaluationPrompt = `
Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

Consider:
1. Have we gathered sufficient information to provide a comprehensive answer?
2. Are there any gaps in the information that require additional searches or scraping?
3. Is the current information accurate and complete enough?
4. Would additional tool calls significantly improve the response quality?

Respond with a JSON object containing:
{
  "shouldContinue": true/false,
  "reasoning": "Brief explanation of why more rounds are or aren't needed",
  "confidence": 1-10 (how confident you are in this decision)
}

Current round: ${round}
`;

    console.log(`[EVALUATION] Using global chat session for round ${round} evaluation`);
    // Use the safe chat messaging system
    const response = await sendChatMessage(evaluationPrompt);

    const responseText = response.text || "{}";
    const evaluation = JSON.parse(responseText.replace(/```json\n?|\n?```/g, ''));

    console.log(`\n=== AI EVALUATION (Round ${round}) ===`);
    console.log(`Should continue: ${evaluation.shouldContinue}`);
    console.log(`Reasoning: ${evaluation.reasoning}`);
    console.log(`Confidence: ${evaluation.confidence}/10`);

    return evaluation.shouldContinue && evaluation.confidence >= SEARCH_ROUNDS.AI_CONFIDENCE_THRESHOLD;
  } catch (error) {
    console.error("Error in AI evaluation:", error);
    // Fallback: continue if we're under a reasonable limit
    return round < SEARCH_ROUNDS.FALLBACK_MAX_ROUNDS;
  }
}

// Function to handle conversation with multiple rounds of function calling (exactly as in your code)
async function handleConversation(userPrompt: string, conversationHistory: any[] = []) {
  let round = 0;
  let shouldContinue = true;

  // Send the initial user prompt to the global chat session
  let response = await sendChatMessage(userPrompt);

  while (shouldContinue) {
    round++;
    console.log(`\n=== ROUND ${round} ===`);

    // Check if there are function calls
    if (response.functionCalls && response.functionCalls.length > 0) {
      console.log(`Function calls detected: ${response.functionCalls.length}`);

      const functionResults = [];

      for (const functionCall of response.functionCalls) {
        console.log(`Executing function: ${functionCall.name}`);
        console.log(`Parameters:`, functionCall.args);

        let result;
        try {
          if (functionCall.name === 'search_web') {
            result = await performSerperSearch(functionCall.args);
          } else if (functionCall.name === 'scrape_webpage') {
            result = await performSerperScrape(functionCall.args);
          } else if (functionCall.name === 'fetch_gmail') {
            result = await performGmailFetch({ ...functionCall.args, userId: 'current_user', ctx: null });
          } else {
            throw new Error(`Unknown function: ${functionCall.name}`);
          }

          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: result
            }
          });
        } catch (error: any) {
          console.error(`Error executing ${functionCall.name}:`, error.message);
          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: { error: error.message }
            }
          });
        }
      }

      // Send function results back to the global chat session
      const functionResultsText = functionResults.map(result =>
        `Function ${result.functionResponse.name} returned: ${JSON.stringify(result.functionResponse.response, null, 2)}`
      ).join('\n\n');

      response = await sendChatMessage(`Function call results:\n\n${functionResultsText}`);

      // Use AI to decide if more rounds are needed
      shouldContinue = await shouldContinueRounds(round);

      if (!shouldContinue) {
        console.log("\n=== AI DECIDED TO STOP ===");
        console.log("AI determined sufficient information has been gathered. Getting final response...");

        // Get final response without function calling
        response = await sendChatMessage("Based on all the information you've gathered, please provide a comprehensive final response to the original query.", true);

        console.log("\n=== FINAL RESPONSE ===");
        console.log(response.text || "No text response available");
        break;
      }
    } else {
      // No function calls, we have the final response
      console.log("\n=== FINAL RESPONSE ===");
      console.log(response.text || "No text response available");
      shouldContinue = false;
    }
  }

  // This should rarely be reached now since AI decides when to stop
  if (round >= SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS) {
    console.log("\n=== SAFETY LIMIT REACHED ===");
    console.log("Reached absolute safety limit. Providing final summary...");

    response = await sendChatMessage("Based on all the information you've gathered, please provide a comprehensive summary to the original query.", true);

    console.log("\n=== FINAL SUMMARY ===");
    console.log(response.text || "No text response available");
  }

  // Since we're using global chat session, we don't need to manually track conversation history
  // The chat session handles this automatically
  return conversationHistory;
}

// Enhanced handleConversation with progress tracking
async function handleConversationWithProgress(
  userPrompt: string,
  searchId: string,
  ctx: any,
  conversationHistory: any[] = []
) {
  let round = 0;
  let shouldContinue = true;
  let totalToolCalls = 0;
  let sourcesScraped = 0;

  // Initial progress update
  await ctx.runMutation(internal.queries.updateSearchProgress, {
    searchId,
    step: "searching",
    progress: PROGRESS_CONFIG.INITIAL_PROGRESS,
    message: "Starting AI-powered search with multi-round function calling..."
  });

  // Send the initial user prompt to the global chat session
  let response = await sendChatMessage(userPrompt);

  while (shouldContinue) {
    round++;
    console.log(`\n=== ROUND ${round} ===`);

    // Update progress based on round
    const progressValue = Math.min(
      PROGRESS_CONFIG.INITIAL_PROGRESS + (round * PROGRESS_CONFIG.ROUND_PROGRESS_INCREMENT),
      PROGRESS_CONFIG.MAX_ROUND_PROGRESS
    );
    await ctx.runMutation(internal.queries.updateSearchProgress, {
      searchId,
      step: round <= STEP_THRESHOLDS.SEARCHING_ROUNDS ? "searching" :
            round <= STEP_THRESHOLDS.ANALYZING_ROUNDS ? "analyzing" : "processing",
      progress: progressValue,
      message: `Round ${round}: AI analyzing and gathering information...`
    });

    // Check if there are function calls
    if (response.functionCalls && response.functionCalls.length > 0) {
      console.log(`Function calls detected: ${response.functionCalls.length}`);
      totalToolCalls += response.functionCalls.length;

      const functionResults = [];

      for (const functionCall of response.functionCalls) {
        console.log(`Executing function: ${functionCall.name}`);
        console.log(`Parameters:`, functionCall.args);

        // Update progress for each function call
        if (functionCall.name === 'scrape_webpage') {
          sourcesScraped++;
          await ctx.runMutation(internal.queries.updateSearchProgress, {
            searchId,
            step: "scraping",
            progress: Math.min(
              PROGRESS_CONFIG.SCRAPING_BASE_PROGRESS + (sourcesScraped * PROGRESS_CONFIG.SCRAPING_INCREMENT),
              PROGRESS_CONFIG.MAX_SCRAPING_PROGRESS
            ),
            message: `Scraping source ${sourcesScraped}...`,
            sourceUrl: functionCall.args?.url,
            sourceTitle: `Source ${sourcesScraped}`
          });
        } else if (functionCall.name === 'fetch_gmail') {
          await ctx.runMutation(internal.queries.updateSearchProgress, {
            searchId,
            step: "processing",
            progress: Math.min(
              PROGRESS_CONFIG.SCRAPING_BASE_PROGRESS + 5,
              PROGRESS_CONFIG.MAX_SCRAPING_PROGRESS
            ),
            message: `Fetching emails...`,
            sourceTitle: `Gmail: ${functionCall.args?.query || 'All emails'}`
          });
        }

        let result;
        try {
          if (functionCall.name === 'search_web') {
            result = await performSerperSearch(functionCall.args);
          } else if (functionCall.name === 'scrape_webpage') {
            result = await performSerperScrape(functionCall.args);
          } else if (functionCall.name === 'fetch_gmail') {
            result = await performGmailFetch({ ...functionCall.args, userId: ctx.userId || 'current_user', ctx });
          } else {
            throw new Error(`Unknown function: ${functionCall.name}`);
          }

          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: result
            }
          });
        } catch (error: any) {
          console.error(`Error executing ${functionCall.name}:`, error.message);
          functionResults.push({
            functionResponse: {
              name: functionCall.name,
              response: { error: error.message }
            }
          });
        }
      }

      // Send function results back to the global chat session
      const functionResultsText = functionResults.map(result =>
        `Function ${result.functionResponse.name} returned: ${JSON.stringify(result.functionResponse.response, null, 2)}`
      ).join('\n\n');

      response = await sendChatMessage(`Function call results:\n\n${functionResultsText}`);

      // Use AI to decide if more rounds are needed
      shouldContinue = await shouldContinueRounds(round);

      if (!shouldContinue) {
        console.log("\n=== AI DECIDED TO STOP ===");
        console.log("AI determined sufficient information has been gathered. Getting final response...");

        // Update progress for final processing
        await ctx.runMutation(internal.queries.updateSearchProgress, {
          searchId,
          step: "finalizing",
          progress: PROGRESS_CONFIG.FINALIZING_PROGRESS,
          message: "Generating comprehensive final response..."
        });

        // Get final response without function calling
        response = await sendChatMessage("Based on all the information you've gathered, please provide a comprehensive final response to the original query.", true);

        console.log("\n=== FINAL RESPONSE ===");
        console.log(response.text || "No text response available");
        break;
      }
    } else {
      // No function calls, we have the final response
      console.log("\n=== FINAL RESPONSE ===");
      console.log(response.text || "No text response available");
      shouldContinue = false;
    }
  }

  // This should rarely be reached now since AI decides when to stop
  if (round >= SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS) {
    console.log("\n=== SAFETY LIMIT REACHED ===");
    console.log("Reached absolute safety limit. Providing final summary...");

    response = await sendChatMessage("Based on all the information you've gathered, please provide a comprehensive summary to the original query.", true);

    console.log("\n=== FINAL SUMMARY ===");
    console.log(response.text || "No text response available");
  }

  // Complete progress
  await ctx.runMutation(internal.queries.updateSearchProgress, {
    searchId,
    step: "complete",
    progress: PROGRESS_CONFIG.COMPLETE_PROGRESS,
    message: "Search completed successfully!"
  });

  // Extract the final response text from the last response
  let finalResponseText = "No response generated";

  console.log("=== EXTRACTING FINAL RESPONSE ===");
  console.log("Response object:", JSON.stringify(response, null, 2));

  // Try multiple ways to extract the response text
  if (response.text) {
    finalResponseText = response.text;
  } else if (response.candidates && response.candidates.length > 0) {
    const candidate = response.candidates[0];
    if (candidate.content && candidate.content.parts) {
      // Extract text from all text parts
      const textParts = candidate.content.parts
        .filter((part: any) => part.text)
        .map((part: any) => part.text);
      if (textParts.length > 0) {
        finalResponseText = textParts.join('\n');
      }
    }
  } else if (response.parts) {
    // Direct parts array
    const textParts = response.parts
      .filter((part: any) => part.text)
      .map((part: any) => part.text);
    if (textParts.length > 0) {
      finalResponseText = textParts.join('\n');
    }
  }

  console.log("Final response text length:", finalResponseText.length);
  console.log("Final response preview:", finalResponseText.substring(0, 200));

  // Return the updated conversation history and metadata
  return {
    conversationHistory,
    totalToolCalls,
    sourcesScraped,
    rounds: round,
    finalResponse: finalResponseText
  };
}

// Main action that the frontend expects - implements session-aware search with progress tracking
export const searchWebWithProgress = action({
  args: {
    query: v.string(),
    searchId: v.string(),
    sessionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      let currentSessionId = args.sessionId;

      // Create new session if not provided
      if (!currentSessionId) {
        currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        console.log(`[SESSION] Creating new session: ${currentSessionId}`);
      } else {
        console.log(`[SESSION] Using existing session: ${currentSessionId}`);
      }

      // Use conversation logic - global chat session handles history automatically
      const result = await handleConversationWithProgress(
        args.query,
        args.searchId,
        ctx,
        [] // Empty array since global chat session handles history
      );

      // Generate session title from first query if this is a new session
      let sessionTitle = undefined;
      if (!args.sessionId) {
        sessionTitle = args.query.length > SESSION_CONFIG.TITLE_MAX_LENGTH ?
          args.query.substring(0, SESSION_CONFIG.TITLE_MAX_LENGTH - SESSION_CONFIG.TITLE_TRUNCATE_SUFFIX.length) + SESSION_CONFIG.TITLE_TRUNCATE_SUFFIX :
          args.query;
      }

      // Save or update session - no need to store conversation history since chat session handles it
      if (!args.sessionId) {
        await ctx.runMutation(internal.queries.createSession, {
          userId,
          sessionId: currentSessionId,
          title: sessionTitle,
          conversationHistory: JSON.stringify([]), // Empty since chat session handles history
        });
      } else {
        await ctx.runMutation(internal.queries.updateSession, {
          sessionId: currentSessionId,
          conversationHistory: JSON.stringify([]), // Empty since chat session handles history
          messageCount: 0, // Not tracking manually anymore
        });
      }

      // Use the final response from the result
      const finalResponse = result.finalResponse;

      // Save the search to the database with session link
      await ctx.runMutation(internal.queries.insertSearch, {
        userId,
        sessionId: currentSessionId,
        query: args.query,
        results: JSON.stringify({
          conversationHistory: result.conversationHistory,
          toolCalls: result.totalToolCalls,
          sourcesScraped: result.sourcesScraped,
          rounds: result.rounds
        }),
        aiResponse: finalResponse,
        timestamp: Date.now(),
      });

      return {
        query: args.query,
        response: finalResponse,
        toolCalls: result.totalToolCalls,
        sourcesScraped: result.sourcesScraped,
        sessionId: currentSessionId,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Search with progress error:", error);

      // Update progress to show error
      await ctx.runMutation(internal.queries.updateSearchProgress, {
        searchId: args.searchId,
        step: "error",
        progress: 0,
        message: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });

      throw new Error(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Convex action that implements your exact conversation logic
export const conversationSearch = action({
  args: {
    query: v.string(),
    conversationHistory: v.optional(v.array(v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Use the global chat session - no separate sessions
      await handleConversation(args.query, []);

      // Get the final response from the global chat session
      const finalResponse = "Conversation completed"; // Global chat session handles the response

      return {
        query: args.query,
        conversationHistory: [], // Global chat session handles history
        finalResponse,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Conversation search error:", error);
      throw new Error(`Conversation search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Simple search action (for basic queries without conversation)
export const simpleSearch = action({
  args: {
    query: v.string(),
  },
  handler: async (_ctx, args) => {
    try {
      // Use the global chat session - no separate sessions
      await handleConversation(args.query, []);

      const finalResponse = "Search completed"; // Global chat session handles the response

      return {
        query: args.query,
        response: finalResponse,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Simple search error:", error);
      throw new Error(`Simple search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Export the core functions for potential use in other parts of the application
export { performSerperSearch, performSerperScrape, handleConversation };